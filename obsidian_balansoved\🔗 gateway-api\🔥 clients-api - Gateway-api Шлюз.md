Идентификатор - d5domf1ev0daigtm42of
Имя - clients-api
Служебный домен - https://d5domf1ev0daigtm42of.y1haggxy.apigw.yandexcloud.net

---
### Спецификация

```yaml
openapi: 3.0.0
info:
  title: Clients API
  version: 1.0.0
servers:
  - url: https://d5domf1ev0daigtm42of.y1haggxy.apigw.yandexcloud.net

# ИСПРАВЛЕНО: Глобальная конфигурация приведена к правильному формату
x-yc-apigateway:
  cors:
    origin: "*"
    methods:
      - "POST"
      - "OPTIONS"
    allowedHeaders:
      - "Content-Type"
      - "Authorization"
    allowCredentials: true
    maxAge: 3600

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - bearerAuth: []

paths:
  /manage:
    # УДАЛЕНО: Лишний блок 'options'
    post:
      summary: Универсальный метод для управления клиентами
      operationId: manageClients
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4e0nco8ka4c1me3qdrt
        service_account_id: ajek4l2ql5b2e77uo3vb
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firm_id:
                  type: string
                action:
                  type: string
                  enum: [GET, UPSERT, DELETE]
                client_id:
                  type: string
                payload:
                  type: object
              required:
                - firm_id
                - action
      responses:
        '200':
          description: Успешное выполнение.
        '201':
          description: Клиент успешно создан.
        '400':
          description: Неверные параметры.
        '403':
          description: Ошибка авторизации.
        '404':
          description: Клиент не найден.
        '500':
          description: Внутренняя ошибка.
```