import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/get_clients_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/upsert_client_usecase.dart';
import 'package:balansoved_enterprise/features/clients/domain/usecases/delete_client_usecase.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

part 'clients_state.dart';

class ClientsCubit extends Cubit<ClientsState> {
  final GetClientsUseCase getClientsUseCase;
  final UpsertClientUseCase upsertClientUseCase;
  final DeleteClientUseCase deleteClientUseCase;

  ClientsCubit({
    required this.getClientsUseCase,
    required this.upsertClientUseCase,
    required this.deleteClientUseCase,
  }) : super(const ClientsState.initial());

  Future<void> fetchClients(String firmId) async {
    emit(state.copyWith(isLoading: true));
    NetworkLogger.printInfo(
      'ClientsCubit: Starting fetchClients for firmId: $firmId',
    );

    try {
      final result = await getClientsUseCase.call(firmId);

      result.fold(
        (failure) {
          NetworkLogger.printError(
            'ClientsCubit: fetchClients failed with error:',
            failure.message,
          );
          NetworkLogger.printError(
            'ClientsCubit: Full failure object:',
            failure,
          );
          emit(state.copyWith(isLoading: false, error: failure.message));
        },
        (clients) {
          NetworkLogger.printSuccess(
            'ClientsCubit: fetchClients success, loaded ${clients.length} clients',
          );
          emit(state.copyWith(isLoading: false, clients: clients, error: null));
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientsCubit: Unexpected error in fetchClients:',
        e,
        stackTrace,
      );
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }

  Future<void> saveClient(String firmId, ClientEntity client) async {
    emit(state.copyWith(isLoading: true));
    NetworkLogger.printInfo(
      'ClientsCubit: Saving client ${client.name} for firmId: $firmId',
    );
    NetworkLogger.printInfo(
      'ClientsCubit: Client profitTaxTypes: ${client.profitTaxTypes}',
    );

    try {
      final result = await upsertClientUseCase.call(firmId, client);

      result.fold(
        (failure) {
          NetworkLogger.printError(
            'ClientsCubit: saveClient failed with error:',
            failure.message,
          );
          NetworkLogger.printError(
            'ClientsCubit: Full failure object:',
            failure,
          );
          emit(state.copyWith(isLoading: false, error: failure.message));
        },
        (_) {
          NetworkLogger.printSuccess('ClientsCubit: saveClient success');
          fetchClients(firmId); // Обновляем список
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientsCubit: Unexpected error in saveClient:',
        e,
        stackTrace,
      );
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }

  Future<void> deleteClient(String firmId, String clientId) async {
    emit(state.copyWith(isLoading: true));
    NetworkLogger.printInfo(
      'ClientsCubit: Deleting client $clientId for firmId: $firmId',
    );

    try {
      final result = await deleteClientUseCase.call(firmId, clientId);

      result.fold(
        (failure) {
          NetworkLogger.printError(
            'ClientsCubit: deleteClient failed with error:',
            failure.message,
          );
          NetworkLogger.printError(
            'ClientsCubit: Full failure object:',
            failure,
          );
          emit(state.copyWith(isLoading: false, error: failure.message));
        },
        (_) {
          NetworkLogger.printSuccess('ClientsCubit: deleteClient success');
          fetchClients(firmId); // Обновляем список
        },
      );
    } catch (e, stackTrace) {
      NetworkLogger.printError(
        'ClientsCubit: Unexpected error in deleteClient:',
        e,
        stackTrace,
      );
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }
}
