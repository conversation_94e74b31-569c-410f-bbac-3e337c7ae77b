import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/auth/domain/entities/user_entity.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/login_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/register_request_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/refresh_token_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/sign_out_usecase.dart';
import 'package:balansoved_enterprise/features/auth/domain/usecases/check_auth_status_usecase.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final LoginUseCase _loginUseCase;
  final RegisterRequestUseCase _registerUseCase;
  final RefreshTokenUseCase _refreshTokenUseCase;
  final SignOutUseCase _signOutUseCase;
  final CheckAuthStatusUseCase _checkAuthStatusUseCase;
  bool _isLoggingIn = false;

  AuthCubit({
    required LoginUseCase loginUseCase,
    required RegisterRequestUseCase registerRequestUseCase,
    required RefreshTokenUseCase refreshTokenUseCase,
    required SignOutUseCase signOutUseCase,
    required CheckAuthStatusUseCase checkAuthStatusUseCase,
  }) : _loginUseCase = loginUseCase,
       _registerUseCase = registerRequestUseCase,
       _refreshTokenUseCase = refreshTokenUseCase,
       _signOutUseCase = signOutUseCase,
       _checkAuthStatusUseCase = checkAuthStatusUseCase,
       super(AuthInitial());

  Future<void> checkAuth() async {
    NetworkLogger.printInfo('CUBIT: Проверка статуса авторизации');
    final result = await _checkAuthStatusUseCase();
    _emitAuthResult(result);
  }

  Future<void> login({required String email, required String password}) async {
    if (_isLoggingIn || state is AuthAuthenticated) return;
    _isLoggingIn = true;
    NetworkLogger.printInfo('CUBIT: Начинаем авторизацию для: $email');
    emit(AuthLoading());
    final result = await _loginUseCase(
      LoginParams(email: email, password: password),
    );
    result.fold(
      (failure) {
        NetworkLogger.printError('CUBIT: Ошибка авторизации:', failure);
        emit(AuthError(_mapFailure(failure)));
        _isLoggingIn = false;
      },
      (_) async {
        NetworkLogger.printSuccess(
          'CUBIT: Авторизация успешна, проверяем статус',
        );
        await checkAuth();
        _isLoggingIn = false;
      },
    );
  }

  Future<void> registerRequest({
    required String email,
    required String password,
    required String userName,
  }) async {
    NetworkLogger.printInfo('CUBIT: Начинаем регистрацию для: $email');
    emit(AuthLoading());
    final result = await _registerUseCase(
      RegisterRequestParams(
        email: email,
        password: password,
        userName: userName,
      ),
    );
    result.fold(
      (failure) {
        NetworkLogger.printError('CUBIT: Ошибка регистрации:', failure);
        emit(AuthError(_mapFailure(failure)));
      },
      (_) {
        NetworkLogger.printSuccess('CUBIT: Регистрация успешна');
        emit(AuthRegistered());
      },
    );
  }

  Future<void> signOut() async {
    NetworkLogger.printInfo('CUBIT: Выход из системы');
    await _signOutUseCase();
    NetworkLogger.printSuccess('CUBIT: Выход завершен');
    emit(AuthUnauthenticated());
  }

  String _mapFailure(Failure failure) {
    // Показываем детальную информацию об ошибке
    if (failure is ServerFailure &&
        failure.details != null &&
        failure.details!.isNotEmpty) {
      NetworkLogger.printInfo('CUBIT: Детали ошибки:');
      NetworkLogger.printJson('CUBIT: Details:', failure.details);
      // В продакшене можно вернуть только failure.message для пользователя
      // В режиме отладки показываем полную информацию
      return '${failure.message}\n\nДетали: ${failure.details}';
    }
    return failure.message;
  }

  void _emitAuthResult(Either<Failure, Option<UserEntity>> res) {
    res.fold(
      (failure) {
        NetworkLogger.printError('CUBIT: Ошибка проверки статуса:', failure);
        emit(AuthError(_mapFailure(failure)));
      },
      (option) => option.fold(
        () {
          NetworkLogger.printInfo('CUBIT: Пользователь не авторизован');
          emit(AuthUnauthenticated());
        },
        (user) {
          NetworkLogger.printSuccess('CUBIT: Пользователь авторизован');
          emit(AuthAuthenticated(user));
        },
      ),
    );
  }
}
