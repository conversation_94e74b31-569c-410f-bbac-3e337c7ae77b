import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import '../entities/client_entity.dart';

abstract class IClientsRepository {
  Future<Either<Failure, List<ClientEntity>>> getClients(String firmId);
  Future<Either<Failure, Unit>> upsertClient(
    String firmId,
    ClientEntity client,
  );
  Future<Either<Failure, Unit>> deleteClient(String firmId, String clientId);
}
