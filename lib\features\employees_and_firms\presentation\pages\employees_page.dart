import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/employees_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/entities/employee_entity.dart';

// === Role name mapping (technical -> display) ===
const Map<String, String> _roleNamesDisplay = {
  'EMPLOYEE': 'Сотрудник',
  'ADMIN': 'Админ',
  'OWNER': 'Владелец',
};

String _formatRoles(List<String> roles) =>
    roles.map((r) => _roleNamesDisplay[r] ?? r).join(', ');

@RoutePage()
class EmployeesPage extends StatefulWidget {
  const EmployeesPage({super.key});

  @override
  State<EmployeesPage> createState() => _EmployeesPageState();
}

class _EmployeesPageState extends State<EmployeesPage> {
  final TextEditingController _searchController = TextEditingController();
  String _filter = '';
  int _sortColumnIndex = 1; // default Email
  bool _sortAsc = true;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() => _filter = _searchController.text);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    prefixIcon: Icon(Icons.search),
                    hintText: 'Фильтр',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              _ActionButton(
                icon: Icons.person_add,
                label: 'Добавить',
                onPressed: () async {
                  final activeFirm =
                      context.read<ActiveFirmCubit>().state.selectedFirm;
                  if (activeFirm == null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Сначала выберите фирму')),
                    );
                    return;
                  }
                  final email = await showDialog<String>(
                    context: context,
                    builder: (ctx) => _AddEmployeeDialog(),
                  );
                  if (email != null && email.isNotEmpty) {
                    context.read<EmployeesCubit>().addEmployee(
                      activeFirm.id,
                      email.trim(),
                    );
                  }
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: BlocBuilder<ActiveFirmCubit, ActiveFirmState>(
              builder: (context, firmState) {
                if (firmState.isLoading || firmState.selectedFirm == null) {
                  return const _EmployeesLoadingTable();
                }

                return BlocListener<EmployeesCubit, EmployeesState>(
                  listener: (context, state) {
                    if (state.error != null) {
                      ScaffoldMessenger.of(
                        context,
                      ).showSnackBar(SnackBar(content: Text(state.error!)));
                    }
                  },
                  child: BlocBuilder<EmployeesCubit, EmployeesState>(
                    builder: (context, state) {
                      if (state.isLoading) {
                        return const _EmployeesLoadingTable();
                      }

                      List<EmployeeEntity> data =
                          state.employees.where((e) {
                            final search = _filter.toLowerCase();
                            final concat =
                                ('${e.id}${e.email ?? ''}${e.userName ?? ''}${e.roles.join(',')}')
                                    .toLowerCase();
                            return search.isEmpty || concat.contains(search);
                          }).toList();

                      data.sort((a, b) {
                        int res;
                        switch (_sortColumnIndex) {
                          case 0:
                            res = a.id.compareTo(b.id);
                            break;
                          case 1:
                            res = (a.email ?? '').compareTo(b.email ?? '');
                            break;
                          case 2:
                            res = (a.userName ?? '').compareTo(
                              b.userName ?? '',
                            );
                            break;
                          case 3:
                            res = a.roles
                                .join(',')
                                .compareTo(b.roles.join(','));
                            break;
                          default:
                            res = 0;
                        }
                        return _sortAsc ? res : -res;
                      });

                      if (data.isEmpty) {
                        return const Center(child: Text('Нет сотрудников'));
                      }
                      return _EmployeesTable(
                        data: data,
                        sortColumnIndex: _sortColumnIndex,
                        sortAscending: _sortAsc,
                        onSort: (index, asc) {
                          setState(() {
                            _sortColumnIndex = index;
                            _sortAsc = asc;
                          });
                        },
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _EmployeesTable extends StatefulWidget {
  final List<EmployeeEntity> data;
  final int sortColumnIndex;
  final bool sortAscending;
  final void Function(int, bool) onSort;
  const _EmployeesTable({
    required this.data,
    required this.sortColumnIndex,
    required this.sortAscending,
    required this.onSort,
  });

  @override
  State<_EmployeesTable> createState() => _EmployeesTableState();
}

class _EmployeesTableState extends State<_EmployeesTable> {
  final ScrollController _hCtrl = ScrollController();

  @override
  void dispose() {
    _hCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SizedBox.expand(
          child: Scrollbar(
            controller: _hCtrl,
            thumbVisibility: true,
            trackVisibility: true,
            scrollbarOrientation: ScrollbarOrientation.bottom,
            child: SingleChildScrollView(
              controller: _hCtrl,
              scrollDirection: Axis.horizontal,
              child: ConstrainedBox(
                constraints: BoxConstraints(minWidth: constraints.maxWidth),
                child: DataTable(
                  sortColumnIndex: widget.sortColumnIndex,
                  sortAscending: widget.sortAscending,
                  columns: [
                    DataColumn(
                      label: const Text('ID'),
                      onSort: (i, asc) => widget.onSort(i, asc),
                    ),
                    DataColumn(
                      label: const Text('Email'),
                      onSort: (i, asc) => widget.onSort(i, asc),
                    ),
                    DataColumn(
                      label: const Text('Имя'),
                      onSort: (i, asc) => widget.onSort(i, asc),
                    ),
                    DataColumn(
                      label: const Text('Роли'),
                      onSort: (i, asc) => widget.onSort(i, asc),
                    ),
                    const DataColumn(label: Text('Действия')),
                  ],
                  rows:
                      widget.data
                          .map(
                            (e) => DataRow(
                              cells: [
                                DataCell(Text(e.id)),
                                DataCell(Text(e.email ?? '')),
                                DataCell(Text(e.userName ?? '-')),
                                DataCell(Text(_formatRoles(e.roles))),
                                DataCell(
                                  Row(
                                    children: [
                                      if (!e.isOwner)
                                        IconButton(
                                          icon: const Icon(Icons.edit),
                                          tooltip: 'Редактировать роли',
                                          onPressed: () async {
                                            final firm =
                                                context
                                                    .read<ActiveFirmCubit>()
                                                    .state
                                                    .selectedFirm;
                                            if (firm == null) return;

                                            final newIsAdmin =
                                                await showDialog<bool>(
                                                  context: context,
                                                  builder:
                                                      (ctx) => _RolesDialog(
                                                        isAdmin: e.isAdmin,
                                                      ),
                                                );

                                            if (newIsAdmin != null &&
                                                newIsAdmin != e.isAdmin) {
                                              context
                                                  .read<EmployeesCubit>()
                                                  .toggleAdminRole(
                                                    firm.id,
                                                    e.id,
                                                    e.isAdmin,
                                                  );
                                            }
                                          },
                                        ),
                                      if (!e.isOwner)
                                        IconButton(
                                          icon: const Icon(
                                            Icons.delete_outline,
                                          ),
                                          tooltip: 'Удалить сотрудника',
                                          onPressed: () async {
                                            final firm =
                                                context
                                                    .read<ActiveFirmCubit>()
                                                    .state
                                                    .selectedFirm;
                                            if (firm == null) return;
                                            final confirm = await showDialog<
                                              bool
                                            >(
                                              context: context,
                                              builder:
                                                  (ctx) => AlertDialog(
                                                    title: const Text(
                                                      'Удалить сотрудника',
                                                    ),
                                                    content: Text(
                                                      'Вы уверены, что хотите удалить сотрудника ${e.email ?? e.userName}?',
                                                    ),
                                                    actions: [
                                                      TextButton(
                                                        onPressed:
                                                            () => Navigator.pop(
                                                              ctx,
                                                              false,
                                                            ),
                                                        child: const Text(
                                                          'Отмена',
                                                        ),
                                                      ),
                                                      TextButton(
                                                        onPressed:
                                                            () => Navigator.pop(
                                                              ctx,
                                                              true,
                                                            ),
                                                        child: const Text(
                                                          'Удалить',
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                            );
                                            if (confirm == true) {
                                              context
                                                  .read<EmployeesCubit>()
                                                  .deleteEmployee(
                                                    firm.id,
                                                    e.id,
                                                  );
                                            }
                                          },
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          )
                          .toList(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _EmployeesLoadingTable extends StatelessWidget {
  const _EmployeesLoadingTable();

  @override
  Widget build(BuildContext context) {
    return const Align(
      alignment: Alignment.topCenter,
      child: LoadingTile(height: 120, width: double.infinity),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback? onPressed;
  const _ActionButton({
    required this.icon,
    required this.label,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(label),
    );
  }
}

class _AddEmployeeDialog extends StatefulWidget {
  const _AddEmployeeDialog();

  @override
  State<_AddEmployeeDialog> createState() => _AddEmployeeDialogState();
}

class _AddEmployeeDialogState extends State<_AddEmployeeDialog> {
  final TextEditingController _emailController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Добавить сотрудника'),
      content: TextField(
        controller: _emailController,
        decoration: const InputDecoration(labelText: 'Email'),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(_emailController.text),
          child: const Text('Добавить'),
        ),
      ],
    );
  }
}

class _RolesDialog extends StatefulWidget {
  final bool isAdmin;
  const _RolesDialog({required this.isAdmin});

  @override
  State<_RolesDialog> createState() => _RolesDialogState();
}

class _RolesDialogState extends State<_RolesDialog> {
  late bool _isAdmin;

  @override
  void initState() {
    super.initState();
    _isAdmin = widget.isAdmin;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Редактировать роли'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CheckboxListTile(
            value: true,
            onChanged: null,
            dense: true,
            title: const Text('Сотрудник'),
          ),
          CheckboxListTile(
            value: _isAdmin,
            onChanged: (val) => setState(() => _isAdmin = val ?? false),
            dense: true,
            title: const Text('Администратор'),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(null),
          child: const Text('Отмена'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(_isAdmin),
          child: const Text('Подтвердить'),
        ),
      ],
    );
  }
}
