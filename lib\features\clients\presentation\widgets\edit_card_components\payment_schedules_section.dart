import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/presentation/widgets/yes_no_switch.dart';
import 'package:balansoved_enterprise/presentation/widgets/help_tooltip.dart';

class PaymentSchedulesSection extends StatelessWidget {
  final bool isEditing;
  final PaymentSchedule? salaryPayment;
  final bool salaryPaymentEnabled;
  final Function(bool) onSalaryPaymentEnabledChanged;
  final PaymentSchedule? advancePayment;
  final PaymentSchedule? ndflPayment;
  final Function(PaymentSchedule?) onSalaryChanged;
  final Function(PaymentSchedule?) onAdvanceChanged;
  final Function(PaymentSchedule?) onNdflChanged;
  final Function(String, String) copyToClipboard;

  final TextEditingController salaryDateCtrl;
  final FocusNode salaryDateFN;
  final TextEditingController advanceDateCtrl;
  final FocusNode advanceDateFN;
  final TextEditingController ndflDate1Ctrl;
  final FocusNode ndflDate1FN;
  final TextEditingController ndflDate2Ctrl;
  final FocusNode ndflDate2FN;

  const PaymentSchedulesSection({
    super.key,
    required this.isEditing,
    required this.salaryPayment,
    required this.salaryPaymentEnabled,
    required this.onSalaryPaymentEnabledChanged,
    required this.advancePayment,
    required this.ndflPayment,
    required this.onSalaryChanged,
    required this.onAdvanceChanged,
    required this.onNdflChanged,
    required this.copyToClipboard,
    required this.salaryDateCtrl,
    required this.salaryDateFN,
    required this.advanceDateCtrl,
    required this.advanceDateFN,
    required this.ndflDate1Ctrl,
    required this.ndflDate1FN,
    required this.ndflDate2Ctrl,
    required this.ndflDate2FN,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.schedule_outlined, size: 20, color: Colors.orange),
            SizedBox(width: 8),
            Text(
              'Графики платежей',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Настройки выплат
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.settings_outlined,
                    size: 16,
                    color: theme.textTheme.bodySmall?.color,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Настройки выплат',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: theme.textTheme.bodyMedium?.color,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              YesNoSwitch(
                label: 'Выплата зарплаты',
                value: salaryPaymentEnabled,
                isEditing: isEditing,
                onChanged: isEditing ? onSalaryPaymentEnabledChanged : null,
                copyToClipboard: copyToClipboard,
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Графики платежей
        _buildPaymentSchedule(
          context: context,
          label: 'Зарплата',
          schedule: salaryPayment,
          controller: salaryDateCtrl,
          focusNode: salaryDateFN,
          onChanged: onSalaryChanged,
          secondDateLabel: null,
          icon: Icons.account_balance_wallet_outlined,
          helpMessage: 'Задача будет перенесена на ближайший рабочий день, до',
        ),
        const SizedBox(height: 12),
        _buildPaymentSchedule(
          context: context,
          label: 'Аванс',
          schedule: advancePayment,
          controller: advanceDateCtrl,
          focusNode: advanceDateFN,
          onChanged: onAdvanceChanged,
          secondDateLabel: null,
          icon: Icons.payments_outlined,
        ),
        const SizedBox(height: 12),
        _buildPaymentSchedule(
          context: context,
          label: 'НДФЛ',
          schedule: ndflPayment,
          controller: ndflDate1Ctrl,
          focusNode: ndflDate1FN,
          secondController: ndflDate2Ctrl,
          secondFocusNode: ndflDate2FN,
          onChanged: onNdflChanged,
          secondDateLabel: 'Дата 2 (1-31)',
          icon: Icons.receipt_long_outlined,
          helpMessage:
              'Задача будет перенесена на ближайший рабочий день, после',
        ),
      ],
    );
  }

  Widget _buildPaymentSchedule({
    required BuildContext context,
    required String label,
    required PaymentSchedule? schedule,
    required Function(PaymentSchedule?) onChanged,
    required TextEditingController controller,
    required FocusNode focusNode,
    TextEditingController? secondController,
    FocusNode? secondFocusNode,
    String? secondDateLabel,
    IconData? icon,
    String? helpMessage,
  }) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (icon != null) ...[
              Icon(icon, size: 18, color: theme.textTheme.bodySmall?.color),
              const SizedBox(width: 8),
            ],
            Text(label),
            if (helpMessage != null) ...[
              const SizedBox(width: 8),
              HelpTooltip(message: helpMessage),
            ],
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: controller,
                focusNode: focusNode,
                decoration: const InputDecoration(
                  labelText: 'Дата (1-31)',
                  prefixIcon: Icon(Icons.today_outlined, size: 20),
                ),
                keyboardType: isEditing ? TextInputType.number : null,
                inputFormatters:
                    isEditing ? [FilteringTextInputFormatter.digitsOnly] : null,
                readOnly: !isEditing,
                onTap:
                    isEditing
                        ? null
                        : () => copyToClipboard(
                          controller.text,
                          'Дата платежа ($label)',
                        ),
                validator: (value) {
                  if (value == null || value.isEmpty) return null;
                  // Извлекаем число из текста (например, "25 число" -> 25)
                  final match = RegExp(r'\d+').firstMatch(value);
                  if (match == null) {
                    return '1-31';
                  }
                  final day = int.tryParse(match.group(0)!);
                  if (day == null || day < 1 || day > 31) {
                    return '1-31';
                  }
                  return null;
                },
                onFieldSubmitted: isEditing ? (_) => focusNode.unfocus() : null,
                onChanged: null,
              ),
            ),
            if (secondDateLabel != null) ...[
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: secondController,
                  focusNode: secondFocusNode,
                  decoration: InputDecoration(
                    labelText: secondDateLabel,
                    prefixIcon: const Icon(Icons.event_outlined, size: 20),
                  ),
                  keyboardType: isEditing ? TextInputType.number : null,
                  inputFormatters:
                      isEditing
                          ? [FilteringTextInputFormatter.digitsOnly]
                          : null,
                  readOnly: !isEditing,
                  onTap:
                      isEditing
                          ? null
                          : () => copyToClipboard(
                            secondController?.text ?? '',
                            '$secondDateLabel ($label)',
                          ),
                  validator: (value) {
                    if (value == null || value.isEmpty) return null;
                    // Извлекаем число из текста (например, "25 число" -> 25)
                    final match = RegExp(r'\d+').firstMatch(value);
                    if (match == null) {
                      return '1-31';
                    }
                    final day = int.tryParse(match.group(0)!);
                    if (day == null || day < 1 || day > 31) {
                      return '1-31';
                    }
                    return null;
                  },
                  onFieldSubmitted:
                      isEditing ? (_) => secondFocusNode?.unfocus() : null,
                  onChanged: null,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }
}
