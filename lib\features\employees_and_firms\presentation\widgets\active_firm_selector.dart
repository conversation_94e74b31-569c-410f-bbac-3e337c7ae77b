import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';

class ActiveFirmSelector extends StatelessWidget {
  const ActiveFirmSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ActiveFirmCubit, ActiveFirmState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const SizedBox(
            height: 32,
            width: 140,
            child: LoadingTile(height: 32, width: 140),
          );
        }

        if (state.firms.isEmpty) {
          return const SizedBox(
            height: 32,
            width: 140,
            child: Center(
              child: Text('Нет фирм', style: TextStyle(fontSize: 14)),
            ),
          );
        }

        return PopupMenuButton<String>(
          initialValue: state.selectedFirm?.id,
          tooltip: 'Выбрать фирму',
          onSelected: (id) {
            final firm = state.firms.firstWhere((f) => f.id == id);
            context.read<ActiveFirmCubit>().selectFirm(firm);
          },
          itemBuilder:
              (ctx) =>
                  state.firms
                      .map(
                        (f) => PopupMenuItem<String>(
                          value: f.id,
                          child: Text(f.name),
                        ),
                      )
                      .toList(),
          child: Row(
            children: [
              const Icon(Icons.business),
              const SizedBox(width: 4),
              Text(state.selectedFirm?.name ?? 'Фирма'),
              const Icon(Icons.arrow_drop_down),
            ],
          ),
        );
      },
    );
  }
}
