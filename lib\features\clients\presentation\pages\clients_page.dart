import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_create_card.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_edit_card.dart';
import 'package:balansoved_enterprise/features/clients/data/models/client_model.dart';

@RoutePage()
class ClientsPage extends StatefulWidget {
  const ClientsPage({super.key});

  @override
  State<ClientsPage> createState() => _ClientsPageState();
}

class _ClientsPageState extends State<ClientsPage> {
  final TextEditingController _searchController = TextEditingController();
  String _filter = '';
  bool _creating = false;
  ClientEntity? _editingClient;

  int _sortColumnIndex = 0;
  bool _sortAsc = true;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() => _filter = _searchController.text);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    prefixIcon: Icon(Icons.search),
                    hintText: 'Фильтр',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                icon: const Icon(Icons.add_business),
                label: const Text('Создать'),
                onPressed: () => setState(() => _creating = true),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child:
                _creating
                    ? ListView(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: ClientCreateCard(
                            onCancel: () => setState(() => _creating = false),
                            onCreated: (client) {
                              final firm =
                                  context
                                      .read<ActiveFirmCubit>()
                                      .state
                                      .selectedFirm;
                              if (firm != null) {
                                context.read<ClientsCubit>().saveClient(
                                  firm.id,
                                  client,
                                );
                              }
                              setState(() => _creating = false);
                            },
                          ),
                        ),
                      ],
                    )
                    : _editingClient != null
                    ? ListView(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: ClientEditCard(
                            client: _editingClient!,
                            onCancel:
                                () => setState(() => _editingClient = null),
                            onSaved: (updated) {
                              final debugJson =
                                  ClientModel.fromEntity(updated).toJson();
                              NetworkLogger.printJson(
                                'SAVE CLIENT JSON =>',
                                debugJson,
                              );

                              final firm =
                                  context
                                      .read<ActiveFirmCubit>()
                                      .state
                                      .selectedFirm;
                              if (firm != null) {
                                context.read<ClientsCubit>().saveClient(
                                  firm.id,
                                  updated,
                                );
                              }
                              setState(() => _editingClient = null);
                            },
                          ),
                        ),
                      ],
                    )
                    : _ClientsTableSection(
                      filterProvider: () => _filter,
                      sortColumnIndex: _sortColumnIndex,
                      sortAsc: _sortAsc,
                      onSort:
                          (i, asc) => setState(() {
                            _sortColumnIndex = i;
                            _sortAsc = asc;
                          }),
                      onEdit:
                          (client) => setState(() {
                            _creating = false;
                            _editingClient = client;
                          }),
                    ),
          ),
        ],
      ),
    );
  }
}

// === Table Section ===
class _ClientsTableSection extends StatefulWidget {
  final String Function() filterProvider;
  final int sortColumnIndex;
  final bool sortAsc;
  final void Function(int, bool) onSort;
  final void Function(ClientEntity) onEdit;
  const _ClientsTableSection({
    required this.filterProvider,
    required this.sortColumnIndex,
    required this.sortAsc,
    required this.onSort,
    required this.onEdit,
  });

  @override
  State<_ClientsTableSection> createState() => _ClientsTableSectionState();
}

class _ClientsTableSectionState extends State<_ClientsTableSection> {
  final ScrollController _hController = ScrollController();

  @override
  void dispose() {
    _hController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ActiveFirmCubit, ActiveFirmState>(
      builder: (context, firmState) {
        if (firmState.isLoading || firmState.selectedFirm == null) {
          return const Align(
            alignment: Alignment.topCenter,
            child: LoadingTile(height: 120, width: double.infinity),
          );
        }

        return BlocListener<ClientsCubit, ClientsState>(
          listener: (context, state) {
            if (state.error != null) {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text(state.error!)));
            }
          },
          child: BlocBuilder<ClientsCubit, ClientsState>(
            builder: (context, state) {
              if (!state.isLoading && state.clients.isEmpty) {
                context.read<ClientsCubit>().fetchClients(
                  firmState.selectedFirm!.id,
                );
              }

              if (state.isLoading) {
                return const Align(
                  alignment: Alignment.topCenter,
                  child: LoadingTile(height: 120, width: double.infinity),
                );
              }

              final search = widget.filterProvider().toLowerCase();
              List<ClientEntity> data =
                  state.clients.where((c) {
                    final concat =
                        '${c.name}${c.shortName ?? ''}${c.comment ?? ''}${c.inn ?? ''}${c.taxSystems.join(',')}'
                            .toLowerCase();
                    return search.isEmpty || concat.contains(search);
                  }).toList();

              data.sort((a, b) {
                int res;
                switch (widget.sortColumnIndex) {
                  case 0:
                    res = (a.shortName ?? '').compareTo(b.shortName ?? '');
                    break;
                  case 1:
                    res = a.name.compareTo(b.name);
                    break;
                  case 2:
                    res = (a.inn ?? '').compareTo(b.inn ?? '');
                    break;
                  case 3:
                    res = (a.taxSystems.isNotEmpty ? a.taxSystems.first : '')
                        .compareTo(
                          b.taxSystems.isNotEmpty ? b.taxSystems.first : '',
                        );
                    break;
                  default:
                    res = 0;
                }
                return widget.sortAsc ? res : -res;
              });

              if (data.isEmpty) {
                return const Center(child: Text('Нет клиентов'));
              }

              return LayoutBuilder(
                builder: (context, constraints) {
                  return SizedBox.expand(
                    child: Scrollbar(
                      controller: _hController,
                      thumbVisibility: true,
                      trackVisibility: true,
                      scrollbarOrientation: ScrollbarOrientation.bottom,
                      child: SingleChildScrollView(
                        controller: _hController,
                        scrollDirection: Axis.horizontal,
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minWidth: constraints.maxWidth,
                          ),
                          child: DataTable(
                            showCheckboxColumn: false,
                            sortColumnIndex: widget.sortColumnIndex,
                            sortAscending: widget.sortAsc,
                            columns: [
                              DataColumn(
                                label: const Text('Сокращенное название'),
                                onSort: (i, asc) => widget.onSort(i, asc),
                              ),
                              DataColumn(
                                label: const Text('Название'),
                                onSort: (i, asc) => widget.onSort(i, asc),
                              ),
                              DataColumn(
                                label: const Text('ИНН'),
                                numeric: false,
                                onSort: (i, asc) => widget.onSort(i, asc),
                              ),
                              DataColumn(
                                label: const Text('Сист. налогообл.'),
                                onSort: (i, asc) => widget.onSort(i, asc),
                              ),
                              const DataColumn(label: Text('Действия')),
                            ],
                            rows:
                                data
                                    .map(
                                      (c) => DataRow(
                                        onSelectChanged:
                                            (_) => widget.onEdit(c),
                                        cells: [
                                          DataCell(
                                            SizedBox(
                                              width: 120,
                                              child: Text(
                                                c.shortName ?? '-',
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ),
                                          DataCell(Text(c.name)),
                                          DataCell(Text(c.inn ?? '-')),
                                          DataCell(
                                            Text(
                                              c.taxSystems.isNotEmpty
                                                  ? c.taxSystems.join(', ')
                                                  : '-',
                                            ),
                                          ),
                                          DataCell(
                                            Row(
                                              children: [
                                                IconButton(
                                                  icon: const Icon(
                                                    Icons.remove_red_eye,
                                                  ),
                                                  tooltip: 'Подробнее',
                                                  onPressed:
                                                      () => widget.onEdit(c),
                                                ),
                                                IconButton(
                                                  icon: const Icon(
                                                    Icons.delete_outline,
                                                  ),
                                                  tooltip: 'Удалить',
                                                  onPressed: () async {
                                                    final firm =
                                                        context
                                                            .read<
                                                              ActiveFirmCubit
                                                            >()
                                                            .state
                                                            .selectedFirm;
                                                    if (firm == null) return;
                                                    final clientsCubit =
                                                        context
                                                            .read<
                                                              ClientsCubit
                                                            >();
                                                    final confirm = await showDialog<
                                                      bool
                                                    >(
                                                      context: context,
                                                      builder:
                                                          (ctx) => AlertDialog(
                                                            title: const Text(
                                                              'Удалить клиента',
                                                            ),
                                                            content: Text(
                                                              'Вы уверены, что хотите удалить клиента ${c.name}?',
                                                            ),
                                                            actions: [
                                                              TextButton(
                                                                onPressed:
                                                                    () =>
                                                                        Navigator.pop(
                                                                          ctx,
                                                                          false,
                                                                        ),
                                                                child:
                                                                    const Text(
                                                                      'Отмена',
                                                                    ),
                                                              ),
                                                              TextButton(
                                                                onPressed:
                                                                    () =>
                                                                        Navigator.pop(
                                                                          ctx,
                                                                          true,
                                                                        ),
                                                                child:
                                                                    const Text(
                                                                      'Удалить',
                                                                    ),
                                                              ),
                                                            ],
                                                          ),
                                                    );
                                                    if (confirm == true) {
                                                      clientsCubit.deleteClient(
                                                        firm.id,
                                                        c.id,
                                                      );
                                                    }
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                    .toList(),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}
