import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';

/// Виджет для выбора средств связи как тегов
class CommunicationMethodsSelector extends StatelessWidget {
  final List<String> selectedMethods;
  final Function(List<String>) onChanged;
  final bool isEditing;

  const CommunicationMethodsSelector({
    super.key,
    required this.selectedMethods,
    required this.onChanged,
    required this.isEditing,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.connect_without_contact_outlined,
              size: 18,
              color: theme.textTheme.bodySmall?.color,
            ),
            const SizedBox(width: 8),
            Text(
              'Средства связи',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: theme.textTheme.bodyMedium?.color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (selectedMethods.isEmpty && !isEditing)
          Text(
            'Не указаны',
            style: TextStyle(
              color: theme.textTheme.bodySmall?.color,
              fontStyle: FontStyle.italic,
            ),
          )
        else
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: [
              // Отображаем выбранные средства связи
              ...selectedMethods.map((method) {
                return Chip(
                  label: Text(method, style: const TextStyle(fontSize: 12)),
                  avatar: _getMethodIcon(method),
                  backgroundColor: theme.colorScheme.primaryContainer,
                  labelStyle: TextStyle(
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                  deleteIcon:
                      isEditing ? const Icon(Icons.close, size: 16) : null,
                  onDeleted: isEditing ? () => _removeMethod(method) : null,
                );
              }),

              // Кнопка добавления нового средства связи
              if (isEditing &&
                  selectedMethods.length <
                      ClientConstants.communicationMethods.length)
                ActionChip(
                  label: const Text(
                    '+ Добавить',
                    style: TextStyle(fontSize: 12),
                  ),
                  avatar: const Icon(Icons.add, size: 16),
                  onPressed: () => _showMethodSelector(context),
                ),
            ],
          ),
      ],
    );
  }

  void _removeMethod(String method) {
    final newMethods = List<String>.from(selectedMethods);
    newMethods.remove(method);
    onChanged(newMethods);
  }

  void _showMethodSelector(BuildContext context) {
    final availableMethods =
        ClientConstants.communicationMethods
            .where((method) => !selectedMethods.contains(method))
            .toList();

    if (availableMethods.isEmpty) return;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Выберите средства связи'),
            content: SizedBox(
              width: double.maxFinite,
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    availableMethods.map((method) {
                      return ActionChip(
                        label: Text(method),
                        avatar: _getMethodIcon(method),
                        onPressed: () {
                          final newMethods = List<String>.from(selectedMethods);
                          newMethods.add(method);
                          onChanged(newMethods);
                          Navigator.of(context).pop();
                        },
                      );
                    }).toList(),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Отмена'),
              ),
            ],
          ),
    );
  }

  Icon _getMethodIcon(String method) {
    switch (method.toLowerCase()) {
      case 'телефон':
        return const Icon(Icons.phone, size: 14);
      case 'whatsapp':
        return const Icon(Icons.message, size: 14);
      case 'telegram':
        return const Icon(Icons.send, size: 14);
      case 'viber':
        return const Icon(Icons.vibration, size: 14);
      case 'email':
        return const Icon(Icons.email, size: 14);
      case 'skype':
        return const Icon(Icons.video_call, size: 14);
      case 'vk':
      case 'instagram':
      case 'facebook':
        return const Icon(Icons.public, size: 14);
      case 'sms':
        return const Icon(Icons.sms, size: 14);
      case 'discord':
        return const Icon(Icons.chat, size: 14);
      case 'zoom':
        return const Icon(Icons.videocam, size: 14);
      case 'почта россии':
        return const Icon(Icons.local_post_office, size: 14);
      default:
        return const Icon(Icons.contact_phone, size: 14);
    }
  }
}
