import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/auth/domain/entities/user_entity.dart';

abstract class IAuthRepository {
  Future<Either<Failure, Unit>> login({
    required String email,
    required String password,
  });
  Future<Either<Failure, Unit>> registerRequest({
    required String email,
    required String password,
    required String userName,
  });
  Future<Either<Failure, Unit>> refreshToken({
    required String email,
    required String password,
  });
  Future<Either<Failure, Unit>> signOut();
  Future<Either<Failure, Option<UserEntity>>> checkAuthStatus();
}
