import 'package:flutter/material.dart';
import '../models.dart';

// ------------------ Чек-лист ------------------
class ChecklistSection extends StatelessWidget {
  final List<ChecklistItem> checklist;
  final VoidCallback onAddItem;
  final Function(ChecklistItem) onRemoveItem;
  final Function(ChecklistItem, bool) onToggleItem;
  final Function(int, int) onReorder;

  const ChecklistSection({
    super.key,
    required this.checklist,
    required this.onAddItem,
    required this.onRemoveItem,
    required this.onToggleItem,
    required this.onReorder,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Чек-лист',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: onAddItem,
              icon: const Icon(Icons.add),
              label: const Text('Добавить'),
            ),
          ],
        ),
        if (checklist.isEmpty)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Text('Чек-лист пуст', style: TextStyle(color: Colors.grey)),
          )
        else
          SizedBox(
            height:
                checklist.length * 72.0, // Примерная высота каждого элемента
            child: ReorderableListView.builder(
              itemCount: checklist.length,
              buildDefaultDragHandles: false,
              onReorder: onReorder,
              itemBuilder: (context, index) {
                final item = checklist[index];
                return Card(
                  key: ValueKey(
                    item.text + index.toString(),
                  ), // Уникальный ключ
                  child: ListTile(
                    leading: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ReorderableDragStartListener(
                          index: index,
                          child: const Icon(
                            Icons.drag_handle,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Checkbox(
                          value: item.completed,
                          onChanged:
                              (value) => onToggleItem(item, value ?? false),
                        ),
                      ],
                    ),
                    title: Text(
                      item.text,
                      style:
                          item.completed
                              ? const TextStyle(
                                decoration: TextDecoration.lineThrough,
                              )
                              : null,
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () => onRemoveItem(item),
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }
}
