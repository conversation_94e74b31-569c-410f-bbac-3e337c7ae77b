Идентификатор - d4e0nco8ka4c1me3qdrt
Описание - 📇 Создать, обновить, удалить или получить информацию о клиенте фирмы

Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: То<PERSON><PERSON>н пользователя с правами `OWNER` или `ADMIN`.
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы, в контексте которой выполняется операция.
		- `action` (string, **обязательно**): Тип операции. Допустимые значения:
			- `GET`: Получить информацию.
			- `UPSERT`: Создать нового или обновить существующего клиента.
			- `DELETE`: Удалить клиента.
		- `client_id` (string, необязательно): ID клиента. Обязателен для `DELETE` и для обновления при `UPSERT`. Если не указан при `GET`, вернется список всех клиентов.
		- `payload` (object, необязательно): Объект с данными клиента. Обязателен для `UPSERT`. Содержит пары `поле: значение` для создания/обновления.

Внутренняя работа:
	1. **Авторизация**: Проверяется JWT токен, извлекается `user_id` запрашивающего.
	2. **Парсинг запроса**: Используется утилита `utils.request_parser` для безопасного извлечения тела запроса.
	3. **Валидация**: Проверяется наличие обязательных полей `firm_id` и `action`.
	4. **Проверка прав**: Выполняется запрос к `firms-database`, чтобы убедиться, что `user_id` принадлежит к указанной `firm_id` и имеет роль `OWNER` или `ADMIN`.
	5. **Определение таблицы**: Динамически формируется имя таблицы в `clients-database` (`clients_{firm_id}`).
	6. **Выполнение операции (`action`)**:
		- **`GET`**: Если `client_id` указан, ищется один клиент. Если нет — возвращается список всех клиентов фирмы.
		- **`UPSERT`**:
			- Если `client_id` **не указан**, создается новая запись с генерацией нового UUID.
			- Если `client_id` **указан**, обновляются поля существующей записи согласно `payload`.
		- **`DELETE`**: Удаляется запись с указанным `client_id`.

На выходе:
	-> `200 OK` (GET): `{"data": [{...}]}` или `{"data": {...}}`
	-> `201 Created` (UPSERT/create): `{"message": "Client created", "client_id": "..."}`
	-> `200 OK` (UPSERT/update): `{"message": "Client updated", "client_id": "..."}`
	-> `200 OK` (DELETE): `{"message": "Client deleted"}`
	-> `400 Bad Request`: Неверные или отсутствующие параметры, ошибка в теле запроса.
	-> `403 Forbidden`: Недостаточно прав для выполнения операции.
	-> `404 Not Found`: Указанный `client_id` не найден.
	-> `500 Internal Server Error`: Ошибка на стороне сервера или БД.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `YDB_ENDPOINT_CLIENTS`, `YDB_DATABASE_CLIENTS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`

---

```python
import json, os, uuid, datetime, pytz, logging
import ydb
from utils import auth_utils, ydb_utils, request_parser

logging.basicConfig(level=logging.INFO)

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass

def check_permissions(session, user_id, firm_id):
    """Проверяет, имеет ли пользователь права OWNER или ADMIN в указанной фирме."""
    query_text = """
        DECLARE $user_id AS Utf8;
        DECLARE $firm_id AS Utf8;
        SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;
    """
    query = session.prepare(query_text)
    result = session.transaction(ydb.SerializableReadWrite()).execute(
        query,
        {"$user_id": user_id, "$firm_id": firm_id},
        commit_tx=True
    )

    if not result[0].rows:
        raise AuthError("User is not a member of the specified firm.")

    roles = json.loads(result[0].rows[0].roles)
    if "OWNER" not in roles and "ADMIN" not in roles:
        raise AuthError("Insufficient permissions. Owner or Admin role required.")
    logging.info(f"User {user_id} has sufficient permissions for firm {firm_id}.")

def get_declare_clauses_and_params(payload):
    """
    Генерирует строку DECLARE и словарь параметров с правильными типами для YDB.
    """
    declare_clauses = ""
    params = {}
    
    type_map = {
        'client_id': ydb.PrimitiveType.Utf8,
        'client_name': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'short_name': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'contacts_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'tax_and_legal_info_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'payment_schedule_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'patents_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'tags_json': ydb.OptionalType(ydb.PrimitiveType.Json),
        'comment': ydb.OptionalType(ydb.PrimitiveType.Utf8),
        'manual_creation_date': ydb.OptionalType(ydb.PrimitiveType.Date),
        'is_active': ydb.OptionalType(ydb.PrimitiveType.Bool),
        'created_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
        'updated_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp),
    }

    for key, value in payload.items():
        if key in type_map:
            if isinstance(type_map[key], ydb.OptionalType) and type_map[key].item == ydb.PrimitiveType.Date and isinstance(value, str):
                try:
                    params[f"${key}"] = datetime.datetime.strptime(value, '%Y-%m-%d').date()
                except ValueError:
                    raise LogicError(f"Invalid date format for '{key}'. Use YYYY-MM-DD.")
            else:
                params[f"${key}"] = value
            
            type_name_str = str(type_map[key])
            if "Optional" in type_name_str:
                type_name = type_name_str.replace("Optional[", "").replace("]", "")
            else:
                type_name = type_name_str
            
            declare_clauses += f"DECLARE ${key} AS {type_name}; "

    return declare_clauses, params


def handler(event, context):
    try:
        # 1. Авторизация
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '): raise AuthError("Unauthorized")

        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload: raise AuthError("Invalid token")

        requesting_user_id = user_payload['user_id']

        # 2. Валидация входных данных с помощью request_parser
        try:
            data = request_parser.parse_request_body(event)
        except ValueError as e:
            raise LogicError(str(e))
            
        firm_id = data.get('firm_id')
        action = data.get('action')
        client_id = data.get('client_id')
        payload = data.get('payload', {})

        if not all([firm_id, action]):
            raise LogicError("firm_id and action are required.")

        # 3. Проверка прав доступа
        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)
        firms_pool.retry_operation_sync(lambda s: check_permissions(s, requesting_user_id, firm_id))

        # 4. Основная логика
        clients_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_CLIENTS"], os.environ["YDB_DATABASE_CLIENTS"])
        clients_pool = ydb.SessionPool(clients_driver)
        table_name = f"clients_{firm_id}"

        def client_transaction(session):
            tx = session.transaction(ydb.SerializableReadWrite())

            if action == "GET":
                if client_id:
                    query_text = f"DECLARE $client_id AS Utf8; SELECT * FROM `{table_name}` WHERE client_id = $client_id;"
                    res = tx.execute(session.prepare(query_text), {"$client_id": client_id})
                    if not res[0].rows: raise NotFoundError(f"Client with id {client_id} not found.")
                    row = res[0].rows[0]
                    data = {c.name: row[c.name] for c in res[0].columns}
                else:
                    res = tx.execute(session.prepare(f"SELECT * FROM `{table_name}`;"))
                    data = [{c.name: r[c.name] for c in res[0].columns} for r in res[0].rows]
                tx.commit()
                return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}

            elif action == "DELETE":
                if not client_id: raise LogicError("client_id is required for DELETE action.")
                query_text = f"DECLARE $client_id AS Utf8; DELETE FROM `{table_name}` WHERE client_id = $client_id;"
                tx.execute(session.prepare(query_text), {"$client_id": client_id})
                tx.commit()
                return {"statusCode": 200, "body": json.dumps({"message": "Client deleted"})}

            elif action == "UPSERT":
                if not payload: raise LogicError("payload is required for UPSERT action.")
                
                payload['updated_at'] = datetime.datetime.now(pytz.utc)

                if client_id: # Update
                    payload['client_id'] = client_id
                    declare_clauses, params = get_declare_clauses_and_params(payload)
                    set_clauses = ", ".join([f"`{k}` = ${k}" for k in payload if k != 'client_id'])
                    query_text = f"{declare_clauses} UPDATE `{table_name}` SET {set_clauses} WHERE client_id = $client_id;"
                    tx.execute(session.prepare(query_text), params)
                    tx.commit()
                    return {"statusCode": 200, "body": json.dumps({"message": "Client updated", "client_id": client_id})}
                else: # Create
                    payload['client_id'] = str(uuid.uuid4())
                    payload['created_at'] = payload['updated_at']
                    declare_clauses, params = get_declare_clauses_and_params(payload)
                    columns = ", ".join([f"`{k}`" for k in payload.keys()])
                    placeholders = ", ".join([f"${k}" for k in payload.keys()])
                    query_text = f"{declare_clauses} UPSERT INTO `{table_name}` ({columns}) VALUES ({placeholders});"
                    tx.execute(session.prepare(query_text), params)
                    tx.commit()
                    return {"statusCode": 201, "body": json.dumps({"message": "Client created", "client_id": payload['client_id']})}
            
            else:
                raise LogicError(f"Invalid action '{action}'. Valid actions are GET, UPSERT, DELETE.")

        return clients_pool.retry_operation_sync(client_transaction)

    except AuthError as e:
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error processing client request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```

---

```json
// Создание нового клиента (UPSERT без client_id) с МАКСИМАЛЬНО полными данными
{
  "httpMethod": "POST",
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.q0nvV5-Nst5Zim-1IR4C0G16eYZEGdnfIAY6a1kf2qs"
  },
  "body": "{\"firm_id\": \"9a33483b-dfad-44a3-a36d-102b498ec0ef\", \"action\": \"UPSERT\", \"payload\": {\"client_name\": \"ООО \\\"Инновационные Технологии Будущего\\\"\", \"short_name\": \"ИТБ\", \"is_active\": true, \"manual_creation_date\": \"2025-02-20\", \"comment\": \"Ключевой клиент, высокий приоритет. Контактное лицо - Мария.\", \"contacts_json\": \"[{\\\"name\\\": \\\"Мария Иванова\\\", \\\"phone\\\": \\\"+79261234567\\\", \\\"email\\\": \\\"<EMAIL>\\\"}]\", \"tax_and_legal_info_json\": \"{\\\"legal_form\\\": \\\"ООО\\\", \\\"tax_system\\\": [\\\"ОСНО\\\"], \\\"profit_tax_type\\\": \\\"Ежемесячные авансы по фактической прибыли\\\", \\\"nds_type\\\": [\\\"Ставка 20%\\\"], \\\"property_tax_type\\\": [\\\"По среднегодовой стоимости\\\"], \\\"reporting_operator\\\": \\\"Контур\\\", \\\"activity_types\\\": [\\\"Разработка ПО\\\", \\\"Консалтинг\\\"]}\", \"payment_schedule_json\": \"{\\\"salary\\\": {\\\"day\\\": 10, \\\"transfer_day\\\": 11}, \\\"prepayment\\\": {\\\"day\\\": 25, \\\"transfer_day\\\": 26}}\", \"patents_json\": \"[{\\\"patent_number\\\": \\\"987654321012345678\\\", \\\"start_date\\\": \\\"2025-01-01\\\", \\\"end_date\\\": \\\"2025-12-31\\\", \\\"issue_date\\\": \\\"2024-12-15\\\", \\\"amount\\\": 120000, \\\"comment\\\": \\\"Патент на IT-услуги на год\\\", \\\"payment_info\\\": {\\\"type\\\": \\\"ежемесячно\\\", \\\"period_start\\\": \\\"2025-01-01\\\", \\\"period_end\\\": \\\"2025-12-31\\\", \\\"amount\\\": 10000, \\\"payment_dates_in_month\\\": [25]}, \\\"reduction_info\\\": {\\\"type\\\": \\\"произвольно\\\", \\\"payments\\\": [{\\\"date\\\": \\\"2025-04-15\\\", \\\"amount\\\": 15000}]}} ]\", \"tags_json\": \"[\\\"VIP\\\", \\\"IT\\\"]\"}}"
}
```

```json
// Обновление клиента (UPSERT с client_id)
{
  "httpMethod": "POST",
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.q0nvV5-Nst5Zim-1IR4C0G16eYZEGdnfIAY6a1kf2qs"
  },
  "body": "{\"firm_id\": \"9a33483b-dfad-44a3-a36d-102b498ec0ef\", \"action\": \"UPSERT\", \"client_id\": \"43fc4810-dd85-4174-95fb-da9fe6a57d5d\", \"payload\": {\"comment\": \"Обновленный комментарий. Связаться в пн.\"}}"
}
```

```json
// Получение одного клиента (GET с client_id)
{
  "httpMethod": "POST",
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.q0nvV5-Nst5Zim-1IR4C0G16eYZEGdnfIAY6a1kf2qs"
  },
  "body": "{\"firm_id\": \"9a33483b-dfad-44a3-a36d-102b498ec0ef\", \"action\": \"GET\", \"client_id\": \"43fc4810-dd85-4174-95fb-da9fe6a57d5d\"}"
}
```

```json
// Получение всех клиентов (GET без client_id)
{
  "httpMethod": "POST",
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.q0nvV5-Nst5Zim-1IR4C0G16eYZEGdnfIAY6a1kf2qs"
  },
  "body": "{\"firm_id\": \"9a33483b-dfad-44a3-a36d-102b498ec0ef\", \"action\": \"GET\"}"
}
```

```json
// Удаление клиента (DELETE)
{
  "httpMethod": "POST",
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.q0nvV5-Nst5Zim-1IR4C0G16eYZEGdnfIAY6a1kf2qs"
  },
  "body": "{\"firm_id\": \"9a33483b-dfad-44a3-a36d-102b498ec0ef\", \"action\": \"DELETE\", \"client_id\": \"43fc4810-dd85-4174-95fb-da9fe6a57d5d\"}"
}
```

