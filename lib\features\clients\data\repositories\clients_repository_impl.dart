import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/auth/data/data_source/auth_local_data_source.dart';
import 'package:balansoved_enterprise/features/clients/data/data_source/clients_remote_data_source.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/repositories/clients_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

class ClientsRepositoryImpl implements IClientsRepository {
  final IClientsRemoteDataSource remote;
  final IAuthLocalDataSource localAuth;

  ClientsRepositoryImpl({required this.remote, required this.localAuth});

  @override
  Future<Either<Failure, List<ClientEntity>>> getClients(String firmId) async {
    try {
      NetworkLogger.printInfo(
        'ClientsRepositoryImpl: Starting getClients for firmId: $firmId',
      );

      NetworkLogger.printInfo(
        'CLIENT REPO: Получаем JWT из локального хранилища',
      );
      final token = await localAuth.getAccessToken();
      if (token == null) {
        NetworkLogger.printError(
          'ClientsRepositoryImpl: No access token available',
          '',
        );
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      NetworkLogger.printInfo(
        'ClientsRepositoryImpl: Got token, calling remote.fetchClients',
      );
      final list = await remote.fetchClients(token, firmId);
      NetworkLogger.printSuccess(
        'CLIENT REPO: Данные получены (${list.length})',
      );
      for (final client in list) {
        NetworkLogger.printInfo(
          'CLIENT REPO: Client ${client.name} - profitTaxTypes: ${client.profitTaxTypes}',
        );
      }
      return Right(list);
    } catch (e) {
      NetworkLogger.printError(
        'ClientsRepositoryImpl: Exception in getClients:',
        e,
      );
      return Left(NetworkFailure(message: 'Ошибка при получении клиентов: $e'));
    }
  }

  @override
  Future<Either<Failure, Unit>> upsertClient(
    String firmId,
    ClientEntity client,
  ) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      await remote.upsertClient(token, firmId, client);
      return const Right(unit);
    } catch (e) {
      NetworkLogger.printError(
        'ClientsRepositoryImpl: Exception in upsertClient:',
        e,
      );
      return Left(NetworkFailure(message: 'Ошибка при сохранении клиента: $e'));
    }
  }

  @override
  Future<Either<Failure, Unit>> deleteClient(
    String firmId,
    String clientId,
  ) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null) {
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      await remote.deleteClient(token, firmId, clientId);
      return const Right(unit);
    } catch (e) {
      NetworkLogger.printError(
        'ClientsRepositoryImpl: Exception in deleteClient:',
        e,
      );
      return Left(NetworkFailure(message: 'Ошибка при удалении клиента: $e'));
    }
  }
}
