import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/domain/repositories/tasks_repository.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/get_tasks_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/get_task_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/save_task_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/domain/usecases/delete_task_usecase.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_state.dart';
import 'package:balansoved_enterprise/features/calendar/presentation/cubit/calendar_cubit.dart';

class TasksCubit extends Cubit<TasksState> {
  final GetTasksUseCase getTasksUseCase;
  final GetTaskUseCase getTaskUseCase;
  final SaveTaskUseCase saveTaskUseCase;
  final DeleteTaskUseCase deleteTaskUseCase;
  final CalendarCubit calendarCubit;

  TasksCubit({
    required this.getTasksUseCase,
    required this.getTaskUseCase,
    required this.saveTaskUseCase,
    required this.deleteTaskUseCase,
    required this.calendarCubit,
  }) : super(TasksInitial());

  TaskRequestParams? _currentParams;

  List<TaskEntity> get tasks {
    final currentState = state;
    if (currentState is TasksLoaded) {
      return currentState.tasks;
    } else if (currentState is TasksLoadingMore) {
      return currentState.tasks;
    }
    return [];
  }

  TaskRequestParams? get currentParams => _currentParams;

  TaskPaginationMeta? get paginationMeta {
    final currentState = state;
    if (currentState is TasksLoaded) {
      return currentState.paginationMeta;
    } else if (currentState is TasksLoadingMore) {
      return currentState.paginationMeta;
    }
    return null;
  }

  bool get canLoadMore {
    final currentState = state;
    if (currentState is TasksLoaded) {
      return currentState.canLoadMore;
    }
    return false;
  }

  bool get isLoadingMore => state is TasksLoadingMore;

  bool get isLoading => state is TasksLoading;
  String? get error =>
      state is TasksError ? (state as TasksError).message : null;

  /// Загрузить задачи по заданным параметрам
  Future<void> fetchTasks(String firmId, TaskRequestParams params) async {
    emit(TasksLoading());

    _currentParams = params;

    final result = await getTasksUseCase(firmId, params);

    result.fold((failure) => emit(TasksError(message: failure.message)), (
      tasksResult,
    ) {
      // Определяем, можно ли загружать ещё задач
      bool canLoadMore = false;
      if (params.viewType == TaskViewType.timeless) {
        // Для бессрочных задач: если загрузилось ровно 100, возможно есть ещё
        canLoadMore = tasksResult.tasks.length == 100;
      }

      emit(
        TasksLoaded(
          result: tasksResult,
          currentParams: params,
          canLoadMore: canLoadMore,
        ),
      );
    });
  }

  /// Загрузить бессрочные задачи (по умолчанию первая страница)
  Future<void> fetchTimelessTasks(String firmId, {int page = 0}) async {
    final params = TaskRequestParams.timeless(page: page);
    await fetchTasks(firmId, params);
  }

  /// Загрузить задачи с крайним сроком за указанный месяц
  Future<void> fetchDatedTasks(
    String firmId, {
    required int month,
    required int year,
  }) async {
    final params = TaskRequestParams.dated(month: month, year: year);
    await fetchTasks(firmId, params);
  }

  /// Загрузить задачи за текущий месяц
  Future<void> fetchCurrentMonthTasks(String firmId) async {
    final params = TaskRequestParams.currentMonth();
    await fetchTasks(firmId, params);
  }

  /// Перейти к следующей странице (только для бессрочных задач)
  Future<void> nextPage(String firmId) async {
    if (_currentParams?.viewType != TaskViewType.timeless) return;

    final meta = paginationMeta;
    if (meta == null || !meta.hasNextPage) return;

    final newParams = _currentParams!.copyWith(page: meta.currentPage + 1);
    await fetchTasks(firmId, newParams);
  }

  /// Перейти к предыдущей странице (только для бессрочных задач)
  Future<void> previousPage(String firmId) async {
    if (_currentParams?.viewType != TaskViewType.timeless) return;

    final meta = paginationMeta;
    if (meta == null || !meta.hasPreviousPage) return;

    final newParams = _currentParams!.copyWith(page: meta.currentPage - 1);
    await fetchTasks(firmId, newParams);
  }

  /// Переключиться на другой месяц (только для срочных задач)
  Future<void> switchToMonth(String firmId, int month, int year) async {
    final params = TaskRequestParams.dated(month: month, year: year);
    await fetchTasks(firmId, params);
  }

  /// Загрузить ещё задач (добавить к существующим)
  Future<void> loadMoreTasks(String firmId) async {
    final currentState = state;

    // Проверяем, что мы в состоянии с загруженными задачами
    if (currentState is! TasksLoaded) return;

    // Проверяем, что это бессрочные задачи и можно загружать ещё
    if (!currentState.canLoadMore ||
        currentState.currentParams.viewType != TaskViewType.timeless) {
      return;
    }

    // Переходим в состояние загрузки дополнительных задач
    emit(
      TasksLoadingMore(
        currentResult: currentState.result,
        currentParams: currentState.currentParams,
      ),
    );

    // Загружаем следующую страницу
    final currentPage = currentState.currentParams.page ?? 0;
    final nextPage = currentPage + 1;
    final nextParams = currentState.currentParams.copyWith(page: nextPage);

    final result = await getTasksUseCase(firmId, nextParams);

    result.fold(
      (failure) {
        // При ошибке возвращаемся к предыдущему состоянию без canLoadMore
        emit(
          TasksLoaded(
            result: currentState.result,
            currentParams: currentState.currentParams,
            canLoadMore: false, // Отключаем кнопку при ошибке
          ),
        );
      },
      (newTasksResult) {
        // Объединяем старые и новые задачи
        final allTasks = [
          ...currentState.result.tasks,
          ...newTasksResult.tasks,
        ];

        // Создаём объединённый результат
        final combinedResult = TasksResult(
          tasks: allTasks,
          paginationMeta: newTasksResult.paginationMeta,
        );

        // Определяем, можно ли загружать ещё
        final canLoadMore = newTasksResult.tasks.length == 100;

        emit(
          TasksLoaded(
            result: combinedResult,
            currentParams: nextParams,
            canLoadMore: canLoadMore,
          ),
        );
      },
    );
  }

  /// Сохранить задачу и перезагрузить текущий список
  Future<void> saveTask(String firmId, TaskEntity task) async {
    final prevState = state;

    final result = await saveTaskUseCase(firmId, task);
    result.fold(
      (failure) {
        emit(
          TasksError(
            message:
                '${failure.message}${failure.details != null ? '\n${failure.details}' : ''}',
          ),
        );
        // Восстанавливаем предыдущее состояние
        if (prevState is TasksLoaded) {
          emit(prevState);
        }
      },
      (taskId) {
        // Перезагружаем текущий список
        if (_currentParams != null) {
          fetchTasks(firmId, _currentParams!);
        }
      },
    );
  }

  /// Удалить задачу и перезагрузить текущий список
  Future<void> deleteTask(String firmId, String taskId) async {
    final result = await deleteTaskUseCase(firmId, taskId);
    result.fold((failure) => emit(TasksError(message: failure.message)), (_) {
      // Перезагружаем текущий список
      if (_currentParams != null) {
        fetchTasks(firmId, _currentParams!);
      }
    });
  }

  /// Обновить статус конкретной задачи с предварительным получением актуальной информации
  Future<void> updateTaskStatus(
    String firmId,
    String taskId,
    String newStatus,
  ) async {
    // Сначала получаем актуальную информацию о задаче
    final getTaskResult = await getTaskUseCase(firmId, taskId);

    await getTaskResult.fold(
      (failure) async {
        emit(
          TasksError(
            message:
                'Ошибка получения актуальной информации о задаче: ${failure.message}',
          ),
        );
      },
      (actualTask) async {
        // Обновляем только статус у актуальной задачи
        final updatedTask = actualTask.copyWith(status: newStatus);

        // Сохраняем обновленную задачу
        final saveResult = await saveTaskUseCase(firmId, updatedTask);

        saveResult.fold(
          (failure) {
            emit(
              TasksError(
                message: 'Ошибка обновления статуса задачи: ${failure.message}',
              ),
            );
          },
          (taskId) {
            // Обновляем задачу в локальном состоянии без полной перезагрузки
            final currentState = state;
            if (currentState is TasksLoaded) {
              final updatedTasks =
                  currentState.result.tasks.map((task) {
                    return task.id == updatedTask.id ? updatedTask : task;
                  }).toList();

              final updatedResult = TasksResult(
                tasks: updatedTasks,
                paginationMeta: currentState.result.paginationMeta,
              );

              emit(
                TasksLoaded(
                  result: updatedResult,
                  currentParams: currentState.currentParams,
                  canLoadMore: currentState.canLoadMore,
                ),
              );
            } else if (currentState is TasksLoadingMore) {
              final updatedTasks =
                  currentState.tasks.map((task) {
                    return task.id == updatedTask.id ? updatedTask : task;
                  }).toList();

              final updatedResult = TasksResult(
                tasks: updatedTasks,
                paginationMeta: currentState.paginationMeta!,
              );

              emit(
                TasksLoaded(
                  result: updatedResult,
                  currentParams: currentState.currentParams,
                  canLoadMore: true,
                ),
              );
            }

            // Уведомляем календарь об изменении задачи
            calendarCubit.updateTaskInCurrentState(updatedTask);
          },
        );
      },
    );
  }

  void clearError() {
    if (state is TasksError) {
      emit(TasksInitial());
    }
  }
}
