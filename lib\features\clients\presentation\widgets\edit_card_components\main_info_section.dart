import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/form_widgets.dart';
import 'package:intl/intl.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_picker_dialog.dart';

class MainInfoSection extends StatelessWidget {
  final bool isEditing;
  final TextEditingController nameCtrl;
  final TextEditingController shortNameCtrl;
  final TextEditingController innCtrl;
  final TextEditingController kppCtrl;
  final TextEditingController commentCtrl;
  final TextEditingController creationDateCtrl;
  final GlobalKey creationDateKey;
  final FocusNode creationDateFN;
  final DateTime? creationDate;
  final String? ownershipForm;
  final Function(DateTime?) onDateChanged;
  final Function(String?) onOwnershipFormChanged;
  final Function(String, String) copyToClipboard;

  const MainInfoSection({
    super.key,
    required this.isEditing,
    required this.nameCtrl,
    required this.shortNameCtrl,
    required this.innCtrl,
    required this.kppCtrl,
    required this.commentCtrl,
    required this.creationDate,
    required this.ownershipForm,
    required this.onDateChanged,
    required this.onOwnershipFormChanged,
    required this.copyToClipboard,
    required this.creationDateCtrl,
    required this.creationDateKey,
    required this.creationDateFN,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.info_outline, size: 20, color: Colors.blue),
            SizedBox(width: 8),
            Text(
              'Основная информация',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.smallSpacing),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: nameCtrl,
                decoration: const InputDecoration(
                  labelText: 'Название *',
                  prefixIcon: Icon(Icons.business, size: 20),
                ),
                readOnly: !isEditing,
                onTap:
                    isEditing
                        ? null
                        : () => copyToClipboard(nameCtrl.text, 'Название'),
                validator:
                    (v) =>
                        (v == null || v.trim().isEmpty)
                            ? 'Введите название'
                            : null,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: shortNameCtrl,
                decoration: const InputDecoration(
                  labelText: 'Сокращённое название',
                  prefixIcon: Icon(Icons.short_text, size: 20),
                ),
                readOnly: !isEditing,
                onTap:
                    isEditing
                        ? null
                        : () => copyToClipboard(
                          shortNameCtrl.text,
                          'Сокращённое название',
                        ),
              ),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.fieldSpacing),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: innCtrl,
                decoration: const InputDecoration(
                  labelText: 'ИНН',
                  prefixIcon: Icon(
                    Icons.confirmation_number_outlined,
                    size: 20,
                  ),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                readOnly: !isEditing,
                onTap:
                    isEditing
                        ? null
                        : () => copyToClipboard(innCtrl.text, 'ИНН'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: kppCtrl,
                decoration: const InputDecoration(
                  labelText: 'КПП',
                  prefixIcon: Icon(Icons.pin_outlined, size: 20),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                readOnly: !isEditing,
                onTap:
                    isEditing
                        ? null
                        : () => copyToClipboard(kppCtrl.text, 'КПП'),
              ),
            ),
          ],
        ),
        const SizedBox(height: ClientConstants.fieldSpacing),
        Row(
          children: [
            Expanded(
              child: DateInputFormField(
                fieldKey: creationDateKey,
                controller: creationDateCtrl,
                focusNode: creationDateFN,
                labelText: 'Дата создания',
                prefixIcon: Icons.calendar_today,
                isEditing: isEditing,
                onIconTap: () async {
                  final date = await SmartDatePickerDialog.show(
                    context: context,
                    initialDate: creationDate,
                    firstDate: DateTime(2000),
                    lastDate: DateTime.now(),
                    helpText: 'Выберите дату создания',
                    allowClear: true,
                  );
                  if (date != null) {
                    onDateChanged(date);
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return null; // Необязательное поле
                  }
                  try {
                    DateFormat('dd.MM.yyyy').parseStrict(value);
                    return null;
                  } catch (e) {
                    return 'Формат: дд.мм.гггг';
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(child: _buildOwnershipFormField(context)),
          ],
        ),
        const SizedBox(height: ClientConstants.fieldSpacing),
        TextFormField(
          controller: commentCtrl,
          decoration: const InputDecoration(
            labelText: 'Комментарий',
            prefixIcon: Icon(Icons.note_alt_outlined, size: 20),
          ),
          maxLines: 3,
          readOnly: !isEditing,
          onTap:
              isEditing
                  ? null
                  : () => copyToClipboard(commentCtrl.text, 'Комментарий'),
        ),
      ],
    );
  }

  Widget _buildOwnershipFormField(BuildContext context) {
    final theme = Theme.of(context);
    if (isEditing) {
      return DropdownButtonFormField<String>(
        value: ownershipForm,
        decoration: const InputDecoration(
          labelText: 'Форма собственности',
          prefixIcon: Icon(Icons.account_balance_outlined, size: 20),
        ),
        items:
            ClientConstants.ownershipForms
                .map((form) => DropdownMenuItem(value: form, child: Text(form)))
                .toList(),
        onChanged: onOwnershipFormChanged,
      );
    }

    return GestureDetector(
      onTap: () => copyToClipboard(ownershipForm ?? '', 'Форма собственности'),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            Icon(
              Icons.account_balance_outlined,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                ownershipForm ?? 'Не указано',
                style: TextStyle(
                  color: ownershipForm == null ? Colors.grey : null,
                ),
              ),
            ),
            const Icon(Icons.copy, size: 16, color: Colors.grey),
          ],
        ),
      ),
    );
  }
}
