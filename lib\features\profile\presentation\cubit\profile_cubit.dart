import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/profile/domain/entities/profile_entity.dart';
import 'package:balansoved_enterprise/features/profile/domain/usecases/get_profile_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  final GetProfileUseCase _getProfile;
  final ActiveFirmCubit _activeFirmCubit;

  ProfileCubit(this._getProfile, this._activeFirmCubit)
    : super(ProfileInitial());

  ProfileEntity? _cache;
  bool _isFetching = false;

  Future<void> fetchProfile() async {
    if (_cache != null) {
      if (!isClosed) emit(ProfileLoaded(_cache!));
      return;
    }

    if (_isFetching) return;
    _isFetching = true;

    if (!isClosed) emit(ProfileLoading());
    final res = await _getProfile();
    _isFetching = false;

    res.fold(
      (failure) {
        if (!isClosed) emit(ProfileError(_mapFailure(failure)));
      },
      (entity) {
        _cache = entity;
        _activeFirmCubit.setFirms(entity.firms);
        if (!isClosed) emit(ProfileLoaded(entity));
      },
    );
  }

  String _mapFailure(Failure f) => f.details ?? f.message;
}
