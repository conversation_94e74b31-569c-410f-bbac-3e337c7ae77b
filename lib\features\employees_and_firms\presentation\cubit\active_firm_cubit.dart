import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/entities/firm_entity.dart';

part 'active_firm_state.dart';

class ActiveFirmCubit extends Cubit<ActiveFirmState> {
  ActiveFirmCubit() : super(const ActiveFirmState.initial());

  void setFirms(List<FirmEntity> firms) {
    if (firms.isEmpty) return;
    final current = state.selectedFirm;
    // Если текущая фирма не в новом списке, выбираем первую
    final selected =
        current != null && firms.any((f) => f.id == current.id)
            ? current
            : firms.first;
    emit(
      state.copyWith(firms: firms, selectedFirm: selected, isLoading: false),
    );
  }

  void selectFirm(FirmEntity firm) {
    if (state.selectedFirm?.id == firm.id) return; // Already selected
    emit(state.copyWith(selectedFirm: firm));
  }
}
