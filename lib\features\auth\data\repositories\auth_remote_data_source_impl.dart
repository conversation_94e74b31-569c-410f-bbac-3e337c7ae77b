import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:balansoved_enterprise/core/constants/constants.dart';
import 'package:balansoved_enterprise/core/error/exception.dart';
import '../data_source/auth_remote_data_source.dart';

class AuthRemoteDataSourceImpl implements IAuthRemoteDataSource {
  final Dio? dio;
  final http.Client? httpClient;

  AuthRemoteDataSourceImpl({this.dio, this.httpClient})
    : assert(
        dio != null || httpClient != null,
        'Either dio or httpClient must be provided',
      );

  @override
  Future<void> registerRequest({
    required String email,
    required String password,
    required String userName,
  }) async {
    if (dio != null) {
      return _registerRequestDio(email, password, userName);
    } else {
      return _registerRequestHttp(email, password, userName);
    }
  }

  @override
  Future<String> login({
    required String email,
    required String password,
  }) async {
    if (dio != null) {
      return _loginDio(email, password);
    } else {
      return _loginHttp(email, password);
    }
  }

  @override
  Future<String> refreshToken({
    required String email,
    required String password,
  }) async {
    if (dio != null) {
      return _refreshTokenDio(email, password);
    } else {
      return _refreshTokenHttp(email, password);
    }
  }

  // Dio версии методов
  Future<void> _registerRequestDio(
    String email,
    String password,
    String userName,
  ) async {
    final url = AuthApiUrls.registerRequest();
    final requestBody = {
      'email': email,
      'password': password,
      'user_name': userName,
    };
    final logRequestBody = {
      'email': email,
      'password': '*****',
      'user_name': userName,
    };

    try {
      debugPrint('🔵 [DIO] Отправка запроса регистрации');
      debugPrint('URL: $url');
      debugPrint('Request Body: ${jsonEncode(logRequestBody)}');
      debugPrint('Headers: ${dio!.options.headers}');

      final response = await dio!.post(url, data: requestBody);

      debugPrint('✅ [DIO] Ответ получен');
      debugPrint('Status Code: ${response.statusCode}');
      debugPrint('Response Headers: ${response.headers}');
      debugPrint('Response Body: ${response.data}');

      if (!(response.statusCode == 200 || response.statusCode == 409)) {
        final exception = ServerException(
          message: 'Ошибка регистрации',
          statusCode: response.statusCode,
          requestUrl: url,
          requestHeaders: dio!.options.headers.cast<String, String>(),
          requestBody: jsonEncode(logRequestBody),
          responseBody: response.data?.toString(),
        );
        debugPrint('❌ [DIO] Ошибка регистрации:\n$exception');
        throw exception;
      }

      debugPrint('✅ [DIO] Регистрация успешна');
    } on DioException catch (e, stackTrace) {
      final errorDetails = _extractDioErrorDetails(e);
      final exception = NetworkException(
        message:
            'Ошибка соединения при регистрации: ${errorDetails['message']}',
        originalError: e.toString(),
        requestUrl: url,
        stackTrace: stackTrace.toString(),
      );
      debugPrint('❌ [DIO] Сетевая ошибка регистрации:\n$exception');
      debugPrint('🔍 Детали Dio ошибки:\n${errorDetails['details']}');
      throw exception;
    } catch (e, stackTrace) {
      debugPrint('❌ [DIO] Неожиданная ошибка регистрации: $e');
      debugPrint('Stack Trace: $stackTrace');
      if (e is ServerException || e is NetworkException) rethrow;
      throw ServerException(
        message: 'Неожиданная ошибка регистрации: $e',
        requestUrl: url,
        requestBody: jsonEncode(logRequestBody),
        stackTrace: stackTrace.toString(),
      );
    }
  }

  Future<String> _loginDio(String email, String password) async {
    final url = AuthApiUrls.login();
    final requestBody = {'email': email, 'password': password};
    final logRequestBody = {'email': email, 'password': '*****'};

    try {
      debugPrint('🔵 [DIO] Отправка запроса входа');
      debugPrint('URL: $url');
      debugPrint('Request Body: ${jsonEncode(logRequestBody)}');
      debugPrint('Headers: ${dio!.options.headers}');

      final response = await dio!.post(url, data: requestBody);

      debugPrint('✅ [DIO] Ответ получен');
      debugPrint('Status Code: ${response.statusCode}');
      debugPrint('Response Headers: ${response.headers}');
      debugPrint('Response Body: ${response.data}');

      if (response.statusCode == 200) {
        final token = response.data['token'] as String?;
        if (token != null) {
          debugPrint('✅ [DIO] Токен получен успешно');
          return token;
        }
        final exception = ServerException(
          message: 'Токен отсутствует в ответе сервера',
          statusCode: response.statusCode,
          requestUrl: url,
          requestHeaders: dio!.options.headers.cast<String, String>(),
          requestBody: jsonEncode(logRequestBody),
          responseBody: response.data?.toString(),
        );
        debugPrint('❌ [DIO] Токен отсутствует:\n$exception');
        throw exception;
      } else {
        final exception = ServerException(
          message: 'Ошибка авторизации',
          statusCode: response.statusCode,
          requestUrl: url,
          requestHeaders: dio!.options.headers.cast<String, String>(),
          requestBody: jsonEncode(logRequestBody),
          responseBody: response.data?.toString(),
        );
        debugPrint('❌ [DIO] Ошибка авторизации:\n$exception');
        throw exception;
      }
    } on DioException catch (e, stackTrace) {
      final errorDetails = _extractDioErrorDetails(e);
      final exception = NetworkException(
        message: 'Ошибка соединения при входе: ${errorDetails['message']}',
        originalError: e.toString(),
        requestUrl: url,
        stackTrace: stackTrace.toString(),
      );
      debugPrint('❌ [DIO] Сетевая ошибка входа:\n$exception');
      debugPrint('🔍 Детали Dio ошибки:\n${errorDetails['details']}');
      throw exception;
    } catch (e, stackTrace) {
      debugPrint('❌ [DIO] Неожиданная ошибка входа: $e');
      debugPrint('Stack Trace: $stackTrace');
      if (e is ServerException || e is NetworkException) rethrow;
      throw ServerException(
        message: 'Неожиданная ошибка входа: $e',
        requestUrl: url,
        requestBody: jsonEncode(logRequestBody),
        stackTrace: stackTrace.toString(),
      );
    }
  }

  Future<String> _refreshTokenDio(String email, String password) async {
    final url = AuthApiUrls.refreshToken();
    final requestBody = {'email': email, 'password': password};
    final logRequestBody = {'email': email, 'password': '*****'};

    try {
      debugPrint('🔵 [DIO] Отправка запроса обновления токена');
      debugPrint('URL: $url');
      debugPrint('Request Body: ${jsonEncode(logRequestBody)}');

      final response = await dio!.post(url, data: requestBody);

      debugPrint('✅ [DIO] Ответ получен');
      debugPrint('Status Code: ${response.statusCode}');
      debugPrint('Response Body: ${response.data}');

      if (response.statusCode == 200) {
        final token = response.data['token'] as String?;
        if (token != null) return token;
        throw ServerException(
          message: 'Токен отсутствует в ответе сервера',
          statusCode: response.statusCode,
          requestUrl: url,
          responseBody: response.data?.toString(),
        );
      } else {
        throw ServerException(
          message: 'Ошибка обновления токена',
          statusCode: response.statusCode,
          requestUrl: url,
          responseBody: response.data?.toString(),
        );
      }
    } on DioException catch (e, stackTrace) {
      final errorDetails = _extractDioErrorDetails(e);
      final exception = NetworkException(
        message:
            'Ошибка соединения при обновлении токена: ${errorDetails['message']}',
        originalError: e.toString(),
        requestUrl: url,
        stackTrace: stackTrace.toString(),
      );
      debugPrint('❌ [DIO] Сетевая ошибка обновления токена:\n$exception');
      debugPrint('🔍 Детали Dio ошибки:\n${errorDetails['details']}');
      throw exception;
    } catch (e, stackTrace) {
      debugPrint('❌ [DIO] Неожиданная ошибка обновления токена: $e');
      if (e is ServerException || e is NetworkException) rethrow;
      throw ServerException(
        message: 'Неожиданная ошибка обновления токена: $e',
        requestUrl: url,
        stackTrace: stackTrace.toString(),
      );
    }
  }

  // HTTP версии методов
  Future<void> _registerRequestHttp(
    String email,
    String password,
    String userName,
  ) async {
    final url = AuthApiUrls.registerRequest();
    final requestBody = jsonEncode({
      'email': email,
      'password': password,
      'user_name': userName,
    });
    final logRequestBody = jsonEncode({
      'email': email,
      'password': '*****',
      'user_name': userName,
    });
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    try {
      debugPrint('🔵 [HTTP] Отправка запроса регистрации');
      debugPrint('URL: $url');
      debugPrint('Headers: $headers');
      debugPrint('Request Body: $logRequestBody');

      final response = await httpClient!
          .post(Uri.parse(url), headers: headers, body: requestBody)
          .timeout(const Duration(seconds: 15));

      debugPrint('✅ [HTTP] Ответ получен');
      debugPrint('Status Code: ${response.statusCode}');
      debugPrint('Response Headers: ${response.headers}');
      debugPrint('Response Body: ${response.body}');

      if (!(response.statusCode == 200 || response.statusCode == 409)) {
        final exception = ServerException(
          message: 'Ошибка регистрации',
          statusCode: response.statusCode,
          requestUrl: url,
          requestHeaders: headers,
          requestBody: logRequestBody,
          responseBody: response.body,
        );
        debugPrint('❌ [HTTP] Ошибка регистрации:\n$exception');
        throw exception;
      }

      debugPrint('✅ [HTTP] Регистрация успешна');
    } catch (e, stackTrace) {
      debugPrint('❌ [HTTP] Ошибка регистрации: $e');
      debugPrint('Stack Trace: $stackTrace');
      if (e is ServerException) rethrow;
      throw NetworkException(
        message: 'Ошибка соединения при регистрации: $e',
        originalError: e.toString(),
        requestUrl: url,
        stackTrace: stackTrace.toString(),
      );
    }
  }

  Future<String> _loginHttp(String email, String password) async {
    final url = AuthApiUrls.login();
    final requestBody = jsonEncode({'email': email, 'password': password});
    final logRequestBody = jsonEncode({'email': email, 'password': '*****'});
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    try {
      debugPrint('🔵 [HTTP] Отправка запроса входа');
      debugPrint('URL: $url');
      debugPrint('Headers: $headers');
      debugPrint('Request Body: $logRequestBody');

      final response = await httpClient!
          .post(Uri.parse(url), headers: headers, body: requestBody)
          .timeout(const Duration(seconds: 15));

      debugPrint('✅ [HTTP] Ответ получен');
      debugPrint('Status Code: ${response.statusCode}');
      debugPrint('Response Headers: ${response.headers}');
      debugPrint('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final body = jsonDecode(response.body);
        final token = body['token'] as String?;
        if (token != null) {
          debugPrint('✅ [HTTP] Токен получен успешно');
          return token;
        }
        final exception = ServerException(
          message: 'Токен отсутствует в ответе сервера',
          statusCode: response.statusCode,
          requestUrl: url,
          requestHeaders: headers,
          requestBody: logRequestBody,
          responseBody: response.body,
        );
        debugPrint('❌ [HTTP] Токен отсутствует:\n$exception');
        throw exception;
      } else {
        final exception = ServerException(
          message: 'Ошибка авторизации',
          statusCode: response.statusCode,
          requestUrl: url,
          requestHeaders: headers,
          requestBody: logRequestBody,
          responseBody: response.body,
        );
        debugPrint('❌ [HTTP] Ошибка авторизации:\n$exception');
        throw exception;
      }
    } catch (e, stackTrace) {
      debugPrint('❌ [HTTP] Ошибка входа: $e');
      debugPrint('Stack Trace: $stackTrace');
      if (e is ServerException) rethrow;
      throw NetworkException(
        message: 'Ошибка соединения при входе: $e',
        originalError: e.toString(),
        requestUrl: url,
        stackTrace: stackTrace.toString(),
      );
    }
  }

  Future<String> _refreshTokenHttp(String email, String password) async {
    final url = AuthApiUrls.refreshToken();
    final requestBody = jsonEncode({'email': email, 'password': password});
    final logRequestBody = jsonEncode({'email': email, 'password': '*****'});
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    try {
      debugPrint('🔵 [HTTP] Отправка запроса обновления токена');
      debugPrint('URL: $url');
      debugPrint('Headers: $headers');
      debugPrint('Request Body: $logRequestBody');

      final response = await httpClient!
          .post(Uri.parse(url), headers: headers, body: requestBody)
          .timeout(const Duration(seconds: 15));

      debugPrint('✅ [HTTP] Ответ получен');
      debugPrint('Status Code: ${response.statusCode}');
      debugPrint('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final body = jsonDecode(response.body);
        final token = body['token'] as String?;
        if (token != null) return token;
        throw ServerException(
          message: 'Токен отсутствует в ответе сервера',
          statusCode: response.statusCode,
          requestUrl: url,
          responseBody: response.body,
        );
      } else {
        throw ServerException(
          message: 'Ошибка обновления токена',
          statusCode: response.statusCode,
          requestUrl: url,
          responseBody: response.body,
        );
      }
    } catch (e, stackTrace) {
      debugPrint('❌ [HTTP] Ошибка обновления токена: $e');
      if (e is ServerException) rethrow;
      throw NetworkException(
        message: 'Ошибка соединения при обновлении токена: $e',
        originalError: e.toString(),
        requestUrl: url,
        stackTrace: stackTrace.toString(),
      );
    }
  }

  // Вспомогательный метод для извлечения деталей DioException
  Map<String, String> _extractDioErrorDetails(DioException e) {
    final buffer = StringBuffer();
    buffer.writeln('DioException Details:');
    buffer.writeln('Type: ${e.type}');
    buffer.writeln('Message: ${e.message}');

    if (e.response != null) {
      buffer.writeln('Response Status: ${e.response?.statusCode}');
      buffer.writeln('Response Headers: ${e.response?.headers}');
      buffer.writeln('Response Data: ${e.response?.data}');
    }

    buffer.writeln('Request URL: ${e.requestOptions.uri}');
    buffer.writeln('Request Method: ${e.requestOptions.method}');
    buffer.writeln('Request Headers: ${e.requestOptions.headers}');
    buffer.writeln('Request Data: ${e.requestOptions.data}');

    final message = switch (e.type) {
      DioExceptionType.connectionTimeout => 'Таймаут соединения',
      DioExceptionType.sendTimeout => 'Таймаут отправки',
      DioExceptionType.receiveTimeout => 'Таймаут получения',
      DioExceptionType.badCertificate => 'Проблема с сертификатом',
      DioExceptionType.badResponse => 'Неверный ответ сервера',
      DioExceptionType.cancel => 'Запрос отменен',
      DioExceptionType.connectionError => 'Ошибка соединения',
      DioExceptionType.unknown => 'Неизвестная ошибка',
    };

    return {'message': message, 'details': buffer.toString()};
  }
}
