import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TaskDetailChecklistSection extends StatefulWidget {
  final TaskEntity task;
  final ValueChanged<bool> onDirtyChanged;
  final VoidCallback onSaved;

  const TaskDetailChecklistSection({
    super.key,
    required this.task,
    required this.onDirtyChanged,
    required this.onSaved,
  });

  @override
  State<TaskDetailChecklistSection> createState() =>
      TaskDetailChecklistSectionState();
}

class TaskDetailChecklistSectionState
    extends State<TaskDetailChecklistSection> {
  late List<Map<String, dynamic>> _checklist;
  late List<Map<String, dynamic>> _originalChecklist;
  bool _saving = false;

  bool get _dirty {
    for (int i = 0; i < _checklist.length; i++) {
      final local = _checklist[i]['completed'] == true;
      final original = _originalChecklist[i]['completed'] == true;
      if (local != original) return true;
    }
    return false;
  }

  @override
  void initState() {
    super.initState();
    _originalChecklist =
        widget.task.checklist.map((e) => Map<String, dynamic>.from(e)).toList();
    _checklist =
        _originalChecklist.map((e) => Map<String, dynamic>.from(e)).toList();

    // Schedule a post-frame callback to check initial dirty state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onDirtyChanged(_dirty);
    });
  }

  void _toggleItem(int index) {
    setState(() {
      final cur = _checklist[index]['completed'] == true;
      _checklist[index]['completed'] = !cur;
    });
    widget.onDirtyChanged(_dirty);
  }

  Future<void> saveChecklist() async {
    if (!_dirty) return;
    setState(() => _saving = true);

    final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
    if (firm == null) {
      setState(() => _saving = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Не выбрана фирма')));
      }
      return;
    }

    final updated = widget.task.copyWith(checklist: _checklist);
    await context.read<TasksCubit>().saveTask(firm.id, updated);

    if (mounted) {
      setState(() {
        _saving = false;
        _originalChecklist =
            _checklist.map((e) => Map<String, dynamic>.from(e)).toList();
      });
      widget.onDirtyChanged(_dirty);
      widget.onSaved();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.task.checklist.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Чек-лист',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            if (_dirty)
              TextButton.icon(
                style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                onPressed: _saving ? null : saveChecklist,
                icon:
                    _saving
                        ? const SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.save, size: 16),
                label:
                    _saving
                        ? const Text('Сохранение...')
                        : const Text('Сохранить'),
              ),
          ],
        ),
        const SizedBox(height: 8),
        ..._checklist.asMap().entries.map((entry) {
          final idx = entry.key;
          final item = entry.value;
          final done = item['completed'] == true;
          return InkWell(
            onTap: () => _toggleItem(idx),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Row(
                children: [
                  Icon(
                    done ? Icons.check_box : Icons.check_box_outline_blank,
                    size: 20,
                    color:
                        done
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      item['text'] ?? '',
                      style: TextStyle(
                        decoration:
                            done
                                ? TextDecoration.lineThrough
                                : TextDecoration.none,
                        color:
                            done
                                ? Theme.of(context).disabledColor
                                : Theme.of(context).textTheme.bodyMedium?.color,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ],
    );
  }
}
