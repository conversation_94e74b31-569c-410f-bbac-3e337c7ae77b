// -------------------------------------------------------------
// ⚠️ ВАЖНО — НЕ УДАЛЯТЬ
// -------------------------------------------------------------
// Сущность [ClientEntity] хранит все поля, которые серверная
// функция `edit-client` может принять или вернуть. Даже если
// отдельные свойства пока не используются во фронте, они нужны
// для корректного обмена данными с API clients-api. Поэтому
// НЕ удаляйте и не переименовывайте поля без синхронизации со
// спецификацией Gateway-API и Cloud Function.
// -------------------------------------------------------------

import 'package:equatable/equatable.dart';

/// Сущность клиента (компании, обслуживаемой фирмой).
///
/// Для облегчения первой итерации реализованы только основные поля
/// (ID, названия, контакты, дата создания и комментарий). Остальные
/// параметры из ТЗ могут быть добавлены позднее без нарушения
/// существующего внешнего интерфейса, т.к. [extra] хранит все
/// необязательные данные.
class ClientEntity extends Equatable {
  final String id;
  final String name;
  final String? shortName;
  final String? inn; // ИНН клиента
  final String? kpp; // КПП клиента
  final List<ContactEntity> contacts;
  final DateTime? creationDate;
  final String? comment;

  /// Словарь для дополнительных параметров клиента, например типы налогов, НДС и т.д.
  /// Эти поля отражены на бэкенде, но пока не используются напрямую во фронте.
  final Map<String, dynamic>? extra;

  // Типы (списки)
  final List<String>
  fixedContributionsIP; // ТИП фиксированных взносов ИП (необязательно, много)
  final List<int> fixedContributionsPaymentDate; // даты платежа фикс. взносов
  final List<String>
  contributionsIP1Percent; // ТИП взносов ИП 1% (необязательно, много)
  final List<int> contributionsIP1PercentPaymentDate; // даты платежа взносов 1%
  final String? ownershipForm; // ТИП формы собственности (мин 1, макс 1)
  final List<String> taxSystems; // ТИП системы налогообложения (обяз, много)
  final List<String>
  profitTaxTypes; // ТИП налога на прибыль (мин 1, множественный)
  final List<String> vatTypes; // ТИП НДС (мин 1, макс 2)
  final List<String> propertyTypes; // ТИП имущества (много)
  final String? reportingType; // ТИП отчётности (мин 1, макс 1)
  final String?
  reportingOperator; // ТИП оператора отчётности (мин 1, макс 1) (СБИС, контур, 1с)
  final List<String> exciseGoods; // ТИП подакцизных товары (много)
  final List<String>
  excisePaymentTerms; // ТИП срок оплаты акциза (мин 1, макс 2)
  final String? enpType; // ТИП ЕНП (мин 1, макс 1)
  final List<String> activityTypes; // ТИП виды деятельности (много)
  final String? ndflType; // ТИП НДФЛ (мин 1, макс 1)
  final List<String> additionalTags; // список, доп ТЕГИ (много)

  // Даты выплат (пары чисел от 1 до 31)
  final PaymentSchedule? salaryPayment; // выплата зарплаты
  final PaymentSchedule? advancePayment; // выплата аванса
  final PaymentSchedule? ndflPayment; // уплата ндфл

  // Патенты
  final List<PatentEntity> patents;

  final bool salaryPaymentEnabled;

  const ClientEntity({
    required this.id,
    required this.name,
    this.inn,
    this.kpp,
    this.shortName,
    this.contacts = const [],
    this.creationDate,
    this.comment,
    this.extra,
    this.fixedContributionsIP = const [],
    this.fixedContributionsPaymentDate = const [],
    this.contributionsIP1Percent = const [],
    this.contributionsIP1PercentPaymentDate = const [],
    this.ownershipForm,
    this.taxSystems = const [],
    this.profitTaxTypes = const [],
    this.vatTypes = const [],
    this.propertyTypes = const [],
    this.reportingType,
    this.reportingOperator,
    this.exciseGoods = const [],
    this.excisePaymentTerms = const [],
    this.enpType,
    this.activityTypes = const [],
    this.ndflType,
    this.additionalTags = const [],
    this.salaryPayment,
    this.salaryPaymentEnabled = false,
    this.advancePayment,
    this.ndflPayment,
    this.patents = const [],
  });

  @override
  List<Object?> get props => [
    id,
    name,
    shortName,
    inn,
    kpp,
    contacts,
    creationDate,
    comment,
    extra,
    fixedContributionsIP,
    fixedContributionsPaymentDate,
    contributionsIP1Percent,
    contributionsIP1PercentPaymentDate,
    ownershipForm,
    taxSystems,
    profitTaxTypes,
    vatTypes,
    propertyTypes,
    reportingType,
    reportingOperator,
    exciseGoods,
    excisePaymentTerms,
    enpType,
    activityTypes,
    ndflType,
    additionalTags,
    salaryPayment,
    salaryPaymentEnabled,
    advancePayment,
    ndflPayment,
    patents,
  ];
}

/// Упрощённая сущность контакта компании.
class ContactEntity extends Equatable {
  final String? phone;
  final String? email;
  final String? fullName;
  final List<String> communicationMethods; // Средства связи как теги

  const ContactEntity({
    this.phone,
    this.email,
    this.fullName,
    this.communicationMethods = const [],
  });

  ContactEntity copyWith({
    String? phone,
    String? email,
    String? fullName,
    List<String>? communicationMethods,
  }) {
    return ContactEntity(
      phone: phone ?? this.phone,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      communicationMethods: communicationMethods ?? this.communicationMethods,
    );
  }

  @override
  List<Object?> get props => [phone, email, fullName, communicationMethods];
}

/// График платежей (дата + дата переноса).
class PaymentSchedule extends Equatable {
  final int paymentDate; // 1-31
  final int transferDate; // 1-31, для НДФЛ

  const PaymentSchedule({
    required this.paymentDate,
    required this.transferDate,
  });

  PaymentSchedule copyWith({int? paymentDate, int? transferDate}) {
    return PaymentSchedule(
      paymentDate: paymentDate ?? this.paymentDate,
      transferDate: transferDate ?? this.transferDate,
    );
  }

  @override
  List<Object?> get props => [paymentDate, transferDate];
}

/// Патент.
class PatentEntity extends Equatable {
  final DateTime startDate; // дата начала (не изм)
  final DateTime endDate; // дата конца (изм)
  final DateTime issueDate; // дата выдачи (не изм)
  final double patentAmount; // сумма патента (изм)
  final String patentNumber; // номер патента (огромное целое число)
  final String? patentTitle; // название патента
  final String? comment; // комментарий

  final PatentPayment? payment; // оплата патента
  final PatentReduction? reduction; // уменьшение патента
  final DateTime?
  newPatentApplicationDate; // заявление на новый патент - дата задачи

  const PatentEntity({
    required this.startDate,
    required this.endDate,
    required this.issueDate,
    required this.patentAmount,
    required this.patentNumber,
    this.patentTitle,
    this.comment,
    this.payment,
    this.reduction,
    this.newPatentApplicationDate,
  });

  @override
  List<Object?> get props => [
    startDate,
    endDate,
    issueDate,
    patentAmount,
    patentNumber,
    patentTitle,
    comment,
    payment,
    reduction,
    newPatentApplicationDate,
  ];
}

/// Оплата патента.
class PatentPayment extends Equatable {
  final String type; // "произвольно" / "ежемесячно"
  final List<PaymentDate>? customPayments; // если произвольно
  final MonthlyPayment? monthlyPayment; // если ежемесячно

  const PatentPayment({
    required this.type,
    this.customPayments,
    this.monthlyPayment,
  });

  PatentPayment copyWith({
    String? type,
    List<PaymentDate>? customPayments,
    MonthlyPayment? monthlyPayment,
  }) {
    return PatentPayment(
      type: type ?? this.type,
      customPayments: customPayments ?? this.customPayments,
      monthlyPayment: monthlyPayment ?? this.monthlyPayment,
    );
  }

  @override
  List<Object?> get props => [type, customPayments, monthlyPayment];
}

/// Уменьшение патента.
class PatentReduction extends Equatable {
  final List<PaymentDate>? customReductions; // если произвольно

  const PatentReduction({this.customReductions});

  PatentReduction copyWith({List<PaymentDate>? customReductions}) {
    return PatentReduction(
      customReductions: customReductions ?? this.customReductions,
    );
  }

  @override
  List<Object?> get props => [customReductions];
}

/// Произвольная дата и сумма.
class PaymentDate extends Equatable {
  final DateTime date;
  final double amount;

  const PaymentDate({required this.date, required this.amount});

  @override
  List<Object?> get props => [date, amount];
}

/// Ежемесячная оплата.
class MonthlyPayment extends Equatable {
  final DateTime startDate; // дата начала периода
  final DateTime endDate; // дата конца периода
  final double customAmount; // кастомная сумма
  final List<int> paymentDays; // ДАТЫ оплаты в месяце (числа от 1 до 31)

  const MonthlyPayment({
    required this.startDate,
    required this.endDate,
    required this.customAmount,
    required this.paymentDays,
  });

  MonthlyPayment copyWith({
    DateTime? startDate,
    DateTime? endDate,
    double? customAmount,
    List<int>? paymentDays,
  }) {
    return MonthlyPayment(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      customAmount: customAmount ?? this.customAmount,
      paymentDays: paymentDays ?? this.paymentDays,
    );
  }

  @override
  List<Object?> get props => [startDate, endDate, customAmount, paymentDays];
}
