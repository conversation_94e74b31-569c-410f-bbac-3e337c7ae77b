import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:balansoved_enterprise/router.dart';

@RoutePage()
class UnauthorizedPage extends StatelessWidget {
  const UnauthorizedPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Если пользователь уже авторизован (например, токен был найден при запуске),
    // перенаправляем сразу, не дожидаясь изменения состояния.
    final authState = context.watch<AuthCubit>().state;
    if (authState is AuthAuthenticated) {
      // Используем микро-задачу, чтобы не нарушить цикл build.
      Future.microtask(() => context.router.replaceAll([const HomeRoute()]));
    }
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          // Если пользователь авторизован, перенаправляем на домашнюю страницу
          context.router.replaceAll([const HomeRoute()]);
        }
      },
      child: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Для доступа необходимо авторизоваться'),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.router.pushNamed('/login'),
                child: const Text('Войти'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
