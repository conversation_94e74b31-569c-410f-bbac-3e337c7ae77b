import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/injection_container.dart' as di;
import 'package:balansoved_enterprise/router.dart';
import 'package:balansoved_enterprise/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/employees_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/calendar/presentation/cubit/calendar_cubit.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/cubit/tariffs_and_storage_cubit.dart';
import 'package:intl/date_symbol_data_local.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Настраиваем детальное логирование для отладки
  if (kDebugMode) {
    debugPrint('🚀 [MAIN] Запуск приложения в режиме отладки');
    debugPrint('🔧 [MAIN] Инициализация зависимостей...');
  }

  // Дополнительная инициализация для веб-платформы
  if (kIsWeb) {
    debugPrint('🌐 [MAIN] Инициализация web-специфичных плагинов...');
    // Дополнительное время для инициализации web плагинов
    await Future.delayed(const Duration(milliseconds: 100));
  }

  await di.setupLocator();
  await initializeDateFormatting('ru_RU', null);

  if (kDebugMode) {
    debugPrint('✅ [MAIN] Зависимости инициализированы');
    debugPrint('🌐 [MAIN] Запуск приложения...');
  }

  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthCubit>.value(value: di.sl<AuthCubit>()..checkAuth()),
        BlocProvider<ActiveFirmCubit>.value(value: di.sl<ActiveFirmCubit>()),
        BlocProvider<ProfileCubit>.value(value: di.sl<ProfileCubit>()),
        BlocProvider<EmployeesCubit>.value(value: di.sl<EmployeesCubit>()),
        BlocProvider<ClientsCubit>.value(value: di.sl<ClientsCubit>()),
        BlocProvider<TasksCubit>.value(value: di.sl<TasksCubit>()),
        BlocProvider<CalendarCubit>.value(value: di.sl<CalendarCubit>()),
        BlocProvider<TariffsAndStorageCubit>.value(
          value: di.sl<TariffsAndStorageCubit>(),
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<AuthCubit, AuthState>(
            listener: (context, state) {
              if (state is AuthAuthenticated) {
                // как только авторизованы – загружаем профиль
                context.read<ProfileCubit>().fetchProfile();
              } else if (state is AuthUnauthenticated) {
                // при выходе чистим список фирм
                context.read<ActiveFirmCubit>().setFirms([]);
              }
            },
          ),
          BlocListener<ProfileCubit, ProfileState>(
            listener: (context, state) {
              if (state is ProfileLoaded) {
                context.read<ActiveFirmCubit>().setFirms(state.profile.firms);
              }
            },
          ),
          BlocListener<ActiveFirmCubit, ActiveFirmState>(
            listener: (context, state) {
              if (!state.isLoading && state.selectedFirm != null) {
                context.read<EmployeesCubit>().fetchEmployees(
                  state.selectedFirm!.id,
                );

                // Загружаем клиентов для выбранной фирмы
                context.read<ClientsCubit>().fetchClients(
                  state.selectedFirm!.id,
                );
              }
            },
          ),
        ],
        child: MaterialApp.router(
          routerConfig: GetIt.I<AppRouter>().config(),
          debugShowCheckedModeBanner: false,
          title: 'Balansoved Enterprise',
          themeMode: ThemeMode.system,
          theme: ThemeData(
            brightness: Brightness.light,
            colorScheme: ColorScheme.fromSeed(
              seedColor: Colors.deepPurple,
              brightness: Brightness.light,
            ),
            useMaterial3: true,
          ),
          darkTheme: ThemeData(
            brightness: Brightness.dark,
            colorScheme: ColorScheme.fromSeed(
              seedColor: Colors.deepPurple,
              brightness: Brightness.dark,
            ),
            useMaterial3: true,
          ),
        ),
      ),
    );
  }
}
