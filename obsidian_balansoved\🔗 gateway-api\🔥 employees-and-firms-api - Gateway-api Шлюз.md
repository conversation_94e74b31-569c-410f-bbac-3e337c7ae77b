Идентификатор - d5dgn7jib7h7qjl33tdp
Имя - employees-and-firms-api
Служебный домен - https://d5dgn7jib7h7qjl33tdp.laqt4bj7.apigw.yandexcloud.net

---
### Спецификация

```yaml
openapi: 3.0.0
info:
  title: Employees and Firms Management API
  version: 1.0.0
servers:
  - url: https://d5dgn7jib7h7qjl33tdp.laqt4bj7.apigw.yandexcloud.net

x-yc-apigateway:
  cors:
    origin: "*"
    methods:
      - POST
      - GET
      - OPTIONS
    allowedHeaders:
      - Content-Type
      - Authorization
    allowCredentials: true
    maxAge: 3600

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - bearerAuth: []

paths:
  /get-user-data:
    get:
      summary: Получить все данные пользователя (инфо, фирмы, задачи) по JWT
      operationId: getUserData
      tags: [User]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4e4c3r7udrhh476a309
        service_account_id: ajek4l2ql5b2e77uo3vb
      responses:
        '200':
          description: Успешное выполнение.
        '401':
          description: Ошибка авторизации.
        '404':
          description: Пользователь не найден.
        '500':
          description: Внутренняя ошибка.

  /firms/create:
    post:
      summary: Создать новую фирму
      operationId: createFirm
      tags: [Firms]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4ep4vks5f5ahnbb6i45
        service_account_id: ajek4l2ql5b2e77uo3vb
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firm_name:
                  type: string
              required:
                - firm_name
      responses:
        '201':
          description: Фирма успешно создана.
        '401':
          description: Ошибка авторизации.
        '409':
          description: Пользователь уже является членом другой фирмы.
        '500':
          description: Внутренняя ошибка.

  /employees/create:
    post:
      summary: Добавить существующего пользователя в фирму как сотрудника
      operationId: createEmployee
      tags: [Employees]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4e5ctmd3olk0kcj9hv0
        service_account_id: ajek4l2ql5b2e77uo3vb
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firm_id:
                  type: string
                email:
                  type: string
                roles:
                  type: array
                  items:
                    type: string
              required:
                - firm_id
                - email
      responses:
        '201':
          description: Сотрудник успешно добавлен.
        '403':
          description: Недостаточно прав.
        '404':
          description: Пользователь для добавления не найден.
        '409':
          description: Пользователь уже работает в этой фирме.

  /employees/edit:
    post:
      summary: Редактировать роли или получить информацию о сотруднике
      operationId: editEmployee
      tags: [Employees]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4eim418vpibjvph5ujv
        service_account_id: ajek4l2ql5b2e77uo3vb
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firm_id:
                  type: string
                action:
                  type: string
                  enum: [GET_INFO, ADD_ROLE, REMOVE_ROLE]
                user_id_to_edit:
                  type: string
                role:
                  type: string
              required:
                - firm_id
                - action
      responses:
        '200':
          description: Успешное выполнение.
        '400':
          description: Неверные параметры.
        '403':
          description: Недостаточно прав.
        '404':
          description: Пользователь не найден.

  /employees/delete:
    post:
      summary: Удалить сотрудника из фирмы
      operationId: deleteEmployee
      tags: [Employees]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4e4eh8qs34ce11rmgte
        service_account_id: ajek4l2ql5b2e77uo3vb
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firm_id:
                  type: string
                user_id_to_delete:
                  type: string
              required:
                - firm_id
                - user_id_to_delete
      responses:
        '200':
          description: Сотрудник успешно удален.
        '400':
          description: Неверные параметры.
        '403':
          description: Недостаточно прав.
        '404':
          description: Пользователь не найден.
```