import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/employees_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';
// Для StreamSubscription

import 'task_detail/attachments_section.dart';
import 'task_detail/checklist_section.dart';

class TaskDetailCard extends StatefulWidget {
  final TaskEntity task;
  final VoidCallback onClose;
  final VoidCallback onEdit;

  const TaskDetailCard({
    super.key,
    required this.task,
    required this.onClose,
    required this.onEdit,
  });

  @override
  State<TaskDetailCard> createState() => _TaskDetailCardState();
}

class _TaskDetailCardState extends State<TaskDetailCard> {
  final _checklistKey = GlobalKey<TaskDetailChecklistSectionState>();
  bool _isChecklistDirty = false;

  @override
  Widget build(BuildContext context) {
    final employees = context.read<EmployeesCubit>().state.employees;
    final clients = context.read<ClientsCubit>().state.clients;

    String empName(String id) {
      final match = employees.where((e) => e.id == id);
      if (match.isNotEmpty) {
        final emp = match.first;
        return emp.userName ?? emp.email ?? id;
      }
      return id;
    }

    String clientName(String id) {
      final match = clients.where((cl) => cl.id == id);
      if (match.isNotEmpty) {
        return match.first.name;
      }
      return id;
    }

    return Card(
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Просмотр задачи',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: Text(
                    widget.task.title,
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: widget.onClose,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (widget.task.description != null &&
                widget.task.description!.isNotEmpty)
              Text('Описание: ${widget.task.description}'),
            const SizedBox(height: 12),
            Text('Статус: ${_translateStatus(widget.task.status)}'),
            const SizedBox(height: 8),
            Text('Приоритет: ${_translatePriority(widget.task.priority)}'),
            if (widget.task.dueDate != null) ...[
              const SizedBox(height: 8),
              Text('Срок: ${_formatDate(widget.task.dueDate)}'),
            ],
            const Divider(height: 32),

            _buildListSection('Клиенты', widget.task.clientIds, clientName),
            _buildListSection(
              'Исполнитель',
              widget.task.assigneeIds.isNotEmpty
                  ? [widget.task.assigneeIds.first]
                  : [],
              empName,
            ),
            if (widget.task.assigneeIds.length > 1)
              _buildListSection(
                'Соисполнители',
                widget.task.assigneeIds.sublist(1),
                empName,
              ),
            _buildListSection('Наблюдатели', widget.task.observerIds, empName),
            _buildCreatorSection(widget.task.creatorIds, empName),

            if (_hasAttachments() ||
                _hasChecklist() ||
                _hasReminders() ||
                _hasOptions() ||
                _hasRecurrence() ||
                _hasHolidayRule())
              const Divider(height: 32),

            if (_hasAttachments()) ...[
              TaskDetailAttachmentsSection(
                attachments: widget.task.attachments,
              ),
              const SizedBox(height: 16),
            ],

            if (_hasChecklist()) ...[
              TaskDetailChecklistSection(
                key: _checklistKey,
                task: widget.task,
                onDirtyChanged: (isDirty) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) {
                      setState(() => _isChecklistDirty = isDirty);
                    }
                  });
                },
                onSaved: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Чек-лист сохранен')),
                  );
                },
              ),
              const SizedBox(height: 16),
            ],

            if (_hasReminders()) ...[
              _buildRemindersSection(),
              const SizedBox(height: 16),
            ],

            if (_hasOptions()) ...[
              _buildOptionsSection(),
              const SizedBox(height: 16),
            ],

            if (_hasRecurrence()) ...[
              _buildRecurrenceSection(),
              const SizedBox(height: 16),
            ],

            if (_hasHolidayRule()) ...[
              Text(
                'Правило переноса (праздники): ${_translateHolidayRule(widget.task.holidayTransferRule)}',
              ),
              const SizedBox(height: 16),
            ],

            const SizedBox(height: 24),

            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: widget.onClose,
                  child: const Text('Закрыть'),
                ),
                const SizedBox(width: 16),
                if (_isChecklistDirty)
                  ElevatedButton.icon(
                    onPressed:
                        () => _checklistKey.currentState?.saveChecklist(),
                    icon: const Icon(Icons.save),
                    label: const Text('Сохранить'),
                  )
                else
                  ElevatedButton.icon(
                    onPressed: widget.onEdit,
                    icon: const Icon(Icons.edit),
                    label: const Text('Редактировать'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListSection(
    String title,
    List<String> ids,
    String Function(String) nameResolver,
  ) {
    if (ids.isEmpty) return const SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Wrap(
          spacing: 4,
          children:
              ids.map((id) => Chip(label: Text(nameResolver(id)))).toList(),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildCreatorSection(
    List<String> ids,
    String Function(String) nameResolver,
  ) {
    if (ids.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: const [
          Text('Постановщик', style: TextStyle(fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          Text('не определён'),
          SizedBox(height: 16),
        ],
      );
    }
    return _buildListSection('Постановщик', ids, nameResolver);
  }

  Widget _buildRemindersSection() {
    if (widget.task.reminders.isEmpty) return const SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Напоминания',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        ...widget.task.reminders.map((r) {
          final dtRaw = r['datetime'];
          final roleRaw = r['role'];
          String dateText = '-';
          if (dtRaw is String && dtRaw.isNotEmpty) {
            try {
              final dt = DateTime.parse(dtRaw).toLocal();
              dateText = DateFormat('dd.MM.yyyy HH:mm').format(dt);
            } catch (_) {
              dateText = dtRaw;
            }
          }
          final roleText = _translateRole(roleRaw?.toString());
          return Text('• $dateText ($roleText)');
        }),
      ],
    );
  }

  Widget _buildOptionsSection() {
    if (widget.task.options.isEmpty) return const SizedBox.shrink();
    final labels = {
      'allow_assignee_to_change_due_date': 'Исполнитель может менять сроки',
      'auto_close_on_due_date': 'Автоматически закрыть по сроку',
      'allow_partial_completion': 'Разрешить частичное выполнение',
      'holiday_transfer_rule': 'Перенос при празднике/выходном',
    };
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Параметры', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        ...widget.task.options.entries.map((e) {
          final label = labels[e.key] ?? e.key;
          String value;
          if (e.value is bool) {
            value = e.value ? 'да' : 'нет';
          } else if (e.key == 'holiday_transfer_rule') {
            value = _translateHolidayRule(e.value?.toString());
          } else {
            value = e.value?.toString() ?? '';
          }
          return Text('$label: $value');
        }),
      ],
    );
  }

  Widget _buildRecurrenceSection() {
    final rec = widget.task.recurrence;
    if (rec == null || rec.isEmpty) return const SizedBox.shrink();

    final type = rec['type']?.toString() ?? '';
    if (type.isEmpty || type == 'none') return const SizedBox.shrink();

    if (type == 'еженедельно' || type == 'weekly') {
      final interval = rec['interval'] ?? 1;
      final days = (rec['days'] as List?)?.cast<String>() ?? [];
      final dayNames = days.map(_weekdayName).join(', ');
      return Text('Повторение: каждые $interval нед. ($dayNames)');
    }
    if (type == 'periodic') {
      final interval = rec['interval'] ?? 1;
      return Text('Повторение: каждые $interval периодически');
    }
    return Text('Повторение: $type');
  }

  String _translateHolidayRule(String? rule) {
    switch (rule) {
      case 'next_workday':
        return 'Следующий рабочий день';
      case 'previous_workday':
        return 'Предыдущий рабочий день';
      case 'no_transfer':
        return 'Не переносить';
      default:
        return rule ?? '-';
    }
  }

  String _translateRole(String? role) {
    switch (role) {
      case 'assignee':
        return 'Исполнитель';
      case 'observer':
        return 'Наблюдатель';
      case 'creator':
        return 'Постановщик';
      default:
        return role ?? '-';
    }
  }

  String _weekdayName(String code) {
    switch (code) {
      case 'MO':
        return 'Пн';
      case 'TU':
        return 'Вт';
      case 'WE':
        return 'Ср';
      case 'TH':
        return 'Чт';
      case 'FR':
        return 'Пт';
      case 'SA':
        return 'Сб';
      case 'SU':
        return 'Вс';
      default:
        return code;
    }
  }

  String _translateStatus(String status) {
    switch (status) {
      case 'pending':
        return 'Новая';
      case 'in_progress':
      case 'ongoing':
        return 'Активно';
      case 'testing':
        return 'Тестирование';
      case 'completed':
      case 'done':
        return 'Завершено';
      case 'cancelled':
        return 'Отменено';
      case 'blocked':
        return 'Заблокирована';
      default:
        return status;
    }
  }

  String _translatePriority(String priority) {
    switch (priority) {
      case 'low':
        return 'Низкий';
      case 'medium':
        return 'Средний';
      case 'high':
        return 'Высокий';
      case 'critical':
        return 'Критический';
      default:
        return priority;
    }
  }

  String _formatDate(DateTime? dt) =>
      dt != null ? DateFormat('dd.MM.yyyy').format(dt) : '-';

  bool _hasAttachments() => widget.task.attachments.isNotEmpty;
  bool _hasChecklist() => widget.task.checklist.isNotEmpty;
  bool _hasReminders() => widget.task.reminders.isNotEmpty;
  bool _hasOptions() => widget.task.options.isNotEmpty;
  bool _hasRecurrence() {
    final rec = widget.task.recurrence;
    if (rec == null || rec.isEmpty) return false;
    final type = rec['type']?.toString() ?? '';
    return type.isNotEmpty && type != 'none';
  }

  bool _hasHolidayRule() {
    final rule = widget.task.holidayTransferRule;
    return rule != null && rule.isNotEmpty && rule != 'no_transfer';
  }
}
