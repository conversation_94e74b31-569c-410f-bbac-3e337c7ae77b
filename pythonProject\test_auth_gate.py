import requests
import json
import sys

# --- Конфигурация API ---
AUTH_API_URL = "https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net"
GATE_CHECKER_API_URL = "https://d5dcghbnv9f73st5ej8b.a6hc9vya.apigw.yandexcloud.net"

# --- Данные для входа ---
LOGIN_PAYLOAD = {
    "email": "ctac<PERSON><EMAIL>",
    "password": "458854Mm"
}


def make_request(title: str, method: str, url: str, payload: dict = None, headers: dict = None):
    """Отправляет запрос и выводит детальную информацию."""
    print("=" * 80)
    print(f"--- {title} ---")
    print(f"Method: {method}")
    print(f"URL: {url}")

    if payload:
        print(f"Payload: {json.dumps(payload, indent=2)}")
    if headers:
        # Для отладки выводим часть токена, чтобы не загромождать вывод
        if "Authorization" in headers:
            token_part = headers["Authorization"][:40]
            print(f"Headers: {{'Authorization': '{token_part}...'}}")
        else:
            print(f"Headers: {headers}")

    print("-" * 20)

    try:
        response = requests.request(method, url, json=payload, headers=headers)

        print(f"Status Code: {response.status_code}")
        print("Response Body:")

        try:
            response_json = response.json()
            print(json.dumps(response_json, indent=4, ensure_ascii=False))
        except json.JSONDecodeError:
            print(response.text or "[No Response Body]")

        # Проверяем на ошибки HTTP после вывода ответа для наглядности
        response.raise_for_status()
        return response

    except requests.exceptions.RequestException as e:
        print(f"\n!!! Request Error: {e} !!!")
        return None


if __name__ == "__main__":
    # --- Шаг 1: Получаем актуальный JWT токен ---
    login_response = make_request(
        title="1. Получение JWT токена (auth-api/login)",
        method="POST",
        url=f"{AUTH_API_URL}/login",
        payload=LOGIN_PAYLOAD
    )

    if not login_response:
        print("\n[ERROR] Не удалось выполнить запрос на вход. Тестирование остановлено.", file=sys.stderr)
        sys.exit(1)

    jwt_token = login_response.json().get("token")
    if not jwt_token:
        print("\n[ERROR] Токен не был получен в ответе. Тестирование остановлено.", file=sys.stderr)
        sys.exit(1)

    print("\n[INFO] Токен успешно получен.")

    # --- Шаг 2: Проверяем гейт с валидным токеном ---
    auth_headers = {"Authorization": f"Bearer {jwt_token}"}
    make_request(
        title="2. Проверка гейта с ВАЛИДНЫМ токеном",
        method="GET",
        url=f"{GATE_CHECKER_API_URL}/check",
        headers=auth_headers
    )

    # --- Шаг 3: Проверяем гейт с невалидным токеном для уверенности ---
    invalid_headers = {"Authorization": "Bearer not.a.real.token"}
    make_request(
        title="3. Проверка гейта с НЕВАЛИДНЫМ токеном (ожидаем ошибку 403)",
        method="GET",
        url=f"{GATE_CHECKER_API_URL}/check",
        headers=invalid_headers
    )

    print("=" * 80)
    print("--- Тестирование завершено ---")