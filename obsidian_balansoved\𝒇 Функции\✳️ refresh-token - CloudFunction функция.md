Идентификатор - d4elom5l31834a68a8r7
Описание - Принудительно обновить JWT токен по реквизитам доступа.
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `email`: Email пользователя.
	-> `password`: Пароль пользователя.
Внутренняя работа:
    -> **Использует утилиту `utils.request_parser` для безопасного извлечения и парсинга тела запроса.**
	-> Находит в YDB активного пользователя по `email`.
	-> Сверяет хеш пароля.
	-> Если все верно, принудительно генерирует **новый** JWT токен.
	-> Обновляет `last_login_at` и **перезаписывает** `jwt_token` в базе данных.
На выходе:
	-> `200 OK`: {"token": "<jwt_token>"}
    -> `400 Bad Request`: В случае проблем с телом запроса.
	-> `401 Unauthorized`: {"message": "Invalid credentials."}

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
    - YDB_ENDPOINT - Эндпоинт базы данных
    - YDB_DATABASE - Путь к базе данных
    - SA_KEY_FILE - ydb_sa_key.json
    - JWT_SECRET - Надежная секретная строка

---

#### Финальная версия кода с использованием утилиты `request_parser`

```python
import json
import os
import datetime
import pytz
import logging
import ydb
from utils import ydb_utils, auth_utils, request_parser

logging.getLogger().setLevel(logging.INFO)

def handler(event, context):
    logging.info("--- NEW INVOCATION ---")
    logging.info(f"RAW EVENT: {event}")

    try:
        data = request_parser.parse_request_body(event)
    except ValueError as e:
        logging.error(f"Request body processing error: {e}")
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}

    email = data.get('email')
    password = data.get('password')

    if not all([email, password]):
        logging.error("Email or password not provided in the parsed data.")
        return {"statusCode": 400, "body": json.dumps({"message": "Email and password are required."})}

    driver = ydb_utils.get_ydb_driver()
    pool = ydb.SessionPool(driver)

    def transaction(session):
        tx = session.transaction(ydb.SerializableReadWrite())

        select_query_text = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8;
            SELECT user_id, password_hash FROM users WHERE email = $email AND is_active = true;
        """
        prepared_select = session.prepare(select_query_text)
        result_sets = tx.execute(prepared_select, {'$email': email})
        
        if not result_sets[0].rows:
            tx.rollback()
            return None

        user_data = result_sets[0].rows[0]

        if not auth_utils.verify_password(password, user_data.password_hash):
            tx.rollback()
            return None

        logging.info(f"Force refreshing token for user {email}")
        new_token = auth_utils.generate_jwt(user_data.user_id, email)
        now = datetime.datetime.now(pytz.utc)

        update_query_text = f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); 
            DECLARE $user_id AS Utf8; DECLARE $now AS Timestamp; DECLARE $token AS Utf8;
            UPDATE users SET last_login_at = $now, jwt_token = $token WHERE user_id = $user_id;
        """
        prepared_update = session.prepare(update_query_text)
        tx.execute(
            prepared_update,
            {'$user_id': user_data.user_id, '$now': now, '$token': new_token}
        )
        
        tx.commit()
        return new_token

    try:
        token = pool.retry_operation_sync(transaction)
        if token:
            logging.info(f"Token refreshed successfully for user {email}.")
            return {"statusCode": 200, "body": json.dumps({"token": token})}
        else:
            logging.warning(f"Invalid credentials on token refresh attempt for user {email}.")
            return {"statusCode": 401, "body": json.dumps({"message": "Invalid credentials."})}
    except Exception as e:
        logging.error(f"Critical error during token refresh for user {email}: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```