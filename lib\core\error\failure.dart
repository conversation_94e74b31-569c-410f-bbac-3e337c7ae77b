import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  final String? details;

  const Failure({required this.message, this.details});

  @override
  List<Object?> get props => [message, details];

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('Failure: $message');

    if (details != null && details!.isNotEmpty) {
      buffer.writeln('Details: $details');
    }

    return buffer.toString();
  }
}

class DatabaseFailure extends Failure {
  const DatabaseFailure({required super.message, super.details});
}

class ConnectionFailure extends Failure {
  const ConnectionFailure({required super.message, super.details});
}

class MessageFailure extends Failure {
  const MessageFailure({required super.message, super.details});
}

class ServerFailure extends Failure {
  const ServerFailure({super.message = 'Ошибка сервера', super.details});
}

class CacheFailure extends Failure {
  const CacheFailure({super.message = 'Ошибка кэша', super.details});
}

class NetworkFailure extends Failure {
  const NetworkFailure({super.message = 'Ошибка сети', super.details});
}

class UnexpectedFailure extends Failure {
  const UnexpectedFailure({
    super.message = 'Неожиданная ошибка',
    super.details,
  });
}
