import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/main_info_section.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_sections.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/tax_section.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/payment_schedules_section.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_utils.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_validation_utils.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_edit_controllers.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_edit_state.dart';

class ClientEditCard extends StatefulWidget {
  final ClientEntity client;
  final VoidCallback onCancel;
  final void Function(ClientEntity) onSaved;

  const ClientEditCard({
    super.key,
    required this.client,
    required this.onCancel,
    required this.onSaved,
  });

  @override
  State<ClientEditCard> createState() => _ClientEditCardState();
}

class _ClientEditCardState extends State<ClientEditCard> {
  final _formKey = GlobalKey<FormState>();

  // Утилитные классы для управления состоянием
  late final ClientEditControllers _controllers;
  late final ClientEditState _state;

  @override
  void initState() {
    super.initState();
    _controllers = ClientEditControllers();
    _state = ClientEditState();

    // Инициализация из клиента
    _controllers.initializeFromClient(widget.client);
    _state.initializeFromClient(widget.client);

    _setupFocusListeners();
  }

  void _setupFocusListeners() {
    // Smart parsing listeners for date fields
    _controllers.creationDateFN.addListener(() {
      ClientUtils.parseAndFormatDateField(
        _controllers.creationDateFN,
        _controllers.creationDateCtrl,
        (date) => setState(() => _state.creationDate = date),
      );
    });

    _controllers.fixedContributionsDateFN.addListener(() {
      ClientUtils.parseAndFormatFixedContributionsField(
        _controllers.fixedContributionsDateFN,
        _controllers.fixedContributionsDateCtrl,
        _state.fixedContributionsIP,
        (dates) => setState(() => _state.fixedContributionsPaymentDate = dates),
      );
    });

    _controllers.contributionsIP1PercentDateFN.addListener(() {
      ClientUtils.parseAndFormatFixedContributionsField(
        _controllers.contributionsIP1PercentDateFN,
        _controllers.contributionsIP1PercentDateCtrl,
        _state.contributionsIP1Percent,
        (dates) =>
            setState(() => _state.contributionsIP1PercentPaymentDate = dates),
      );
    });

    _controllers.salaryDateFN.addListener(() {
      ClientUtils.parseAndFormatDayField(
        _controllers.salaryDateFN,
        _controllers.salaryDateCtrl,
        (day) => setState(
          () =>
              _state.salaryPayment =
                  _state.salaryPayment?.copyWith(paymentDate: day) ??
                  PaymentSchedule(paymentDate: day, transferDate: 1),
        ),
      );
    });

    _controllers.advanceDateFN.addListener(() {
      ClientUtils.parseAndFormatDayField(
        _controllers.advanceDateFN,
        _controllers.advanceDateCtrl,
        (day) => setState(
          () =>
              _state.advancePayment =
                  _state.advancePayment?.copyWith(paymentDate: day) ??
                  PaymentSchedule(paymentDate: day, transferDate: 1),
        ),
      );
    });

    _controllers.ndflDate1FN.addListener(() {
      ClientUtils.parseAndFormatDayField(
        _controllers.ndflDate1FN,
        _controllers.ndflDate1Ctrl,
        (day) => setState(
          () =>
              _state.ndflPayment =
                  _state.ndflPayment?.copyWith(paymentDate: day) ??
                  PaymentSchedule(
                    paymentDate: day,
                    transferDate: _state.ndflPayment?.transferDate ?? 1,
                  ),
        ),
      );
    });

    _controllers.ndflDate2FN.addListener(() {
      ClientUtils.parseAndFormatDayField(
        _controllers.ndflDate2FN,
        _controllers.ndflDate2Ctrl,
        (day) => setState(
          () =>
              _state.ndflPayment =
                  _state.ndflPayment?.copyWith(transferDate: day) ??
                  PaymentSchedule(
                    paymentDate: _state.ndflPayment?.paymentDate ?? 1,
                    transferDate: day,
                  ),
        ),
      );
    });
  }

  @override
  void dispose() {
    _controllers.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      color: ClientConstants.cardColor(context),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Редактировать клиента',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    MainInfoSection(
                      isEditing: _state.isEditing,
                      nameCtrl: _controllers.nameCtrl,
                      shortNameCtrl: _controllers.shortNameCtrl,
                      innCtrl: _controllers.innCtrl,
                      kppCtrl: _controllers.kppCtrl,
                      commentCtrl: _controllers.commentCtrl,
                      creationDate: _state.creationDate,
                      ownershipForm: _state.ownershipForm,
                      creationDateCtrl: _controllers.creationDateCtrl,
                      creationDateKey: _controllers.creationDateKey,
                      creationDateFN: _controllers.creationDateFN,
                      onDateChanged: (date) {
                        setState(() {
                          _state.creationDate = date;
                          _controllers.creationDateCtrl.text =
                              date != null
                                  ? DateFormat('dd.MM.yyyy').format(date)
                                  : '';
                        });
                      },
                      onOwnershipFormChanged:
                          (form) => setState(() => _state.ownershipForm = form),
                      copyToClipboard:
                          (text, field) =>
                              ClientUtils.copyToClipboard(context, text, field),
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),

                    ContactsSection(
                      isEditing: _state.isEditing,
                      contacts: _state.contacts,
                      onRemoveContact:
                          (index) =>
                              setState(() => _state.contacts.removeAt(index)),
                      onUpdateContact:
                          (index, contact) =>
                              setState(() => _state.contacts[index] = contact),
                      onAddContact:
                          () => setState(() {
                            _state.contacts.add(
                              const ContactEntity(
                                fullName: '',
                                phone: '',
                                email: '',
                                communicationMethods: [],
                              ),
                            );
                          }),
                      copyToClipboard:
                          (text, field) =>
                              ClientUtils.copyToClipboard(context, text, field),
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),

                    TaxSection(
                      isEditing: _state.isEditing,
                      taxSystems: _state.taxSystems,
                      profitTaxTypes: _state.profitTaxTypes,
                      vatTypes: _state.vatTypes,
                      propertyTypes: _state.propertyTypes,
                      reportingType: _state.reportingType,
                      reportingOperator: _state.reportingOperator,
                      exciseGoods: _state.exciseGoods,
                      excisePaymentTerms: _state.excisePaymentTerms,
                      enpType: _state.enpType,
                      ndflType: _state.ndflType,
                      onTaxSystemAdd:
                          (item) => setState(() => _state.taxSystems.add(item)),
                      onTaxSystemRemove:
                          (item) =>
                              setState(() => _state.taxSystems.remove(item)),
                      onProfitTaxTypeAdd:
                          (item) =>
                              setState(() => _state.profitTaxTypes.add(item)),
                      onProfitTaxTypeRemove:
                          (item) => setState(
                            () => _state.profitTaxTypes.remove(item),
                          ),
                      onVatTypeAdd:
                          (item) => setState(() => _state.vatTypes.add(item)),
                      onVatTypeRemove:
                          (item) =>
                              setState(() => _state.vatTypes.remove(item)),
                      onPropertyTypeAdd:
                          (item) =>
                              setState(() => _state.propertyTypes.add(item)),
                      onPropertyTypeRemove:
                          (item) =>
                              setState(() => _state.propertyTypes.remove(item)),
                      onReportingTypeChanged:
                          (value) =>
                              setState(() => _state.reportingType = value),
                      onReportingOperatorChanged:
                          (value) =>
                              setState(() => _state.reportingOperator = value),
                      onExciseGoodAdd:
                          (item) =>
                              setState(() => _state.exciseGoods.add(item)),
                      onExciseGoodRemove:
                          (item) =>
                              setState(() => _state.exciseGoods.remove(item)),
                      onExcisePaymentTermAdd:
                          (item) => setState(
                            () => _state.excisePaymentTerms.add(item),
                          ),
                      onExcisePaymentTermRemove:
                          (item) => setState(
                            () => _state.excisePaymentTerms.remove(item),
                          ),
                      onEnpTypeChanged:
                          (value) => setState(() => _state.enpType = value),
                      onNdflTypeChanged:
                          (value) => setState(() => _state.ndflType = value),
                      copyToClipboard:
                          (text, field) =>
                              ClientUtils.copyToClipboard(context, text, field),
                      highlightEnp: false,
                      enpKey: _controllers.enpFieldKey,
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),

                    PaymentSchedulesSection(
                      isEditing: _state.isEditing,
                      salaryPayment: _state.salaryPayment,
                      salaryPaymentEnabled: _state.salaryPaymentEnabled,
                      onSalaryPaymentEnabledChanged:
                          (v) =>
                              setState(() => _state.salaryPaymentEnabled = v),
                      advancePayment: _state.advancePayment,
                      ndflPayment: _state.ndflPayment,
                      onSalaryChanged:
                          (sch) => setState(() => _state.salaryPayment = sch),
                      onAdvanceChanged:
                          (sch) => setState(() => _state.advancePayment = sch),
                      onNdflChanged:
                          (sch) => setState(() => _state.ndflPayment = sch),
                      copyToClipboard:
                          (text, field) =>
                              ClientUtils.copyToClipboard(context, text, field),
                      salaryDateCtrl: _controllers.salaryDateCtrl,
                      salaryDateFN: _controllers.salaryDateFN,
                      advanceDateCtrl: _controllers.advanceDateCtrl,
                      advanceDateFN: _controllers.advanceDateFN,
                      ndflDate1Ctrl: _controllers.ndflDate1Ctrl,
                      ndflDate1FN: _controllers.ndflDate1FN,
                      ndflDate2Ctrl: _controllers.ndflDate2Ctrl,
                      ndflDate2FN: _controllers.ndflDate2FN,
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),

                    AdditionalSection(
                      isEditing: _state.isEditing,
                      additionalTags: _state.additionalTags,
                      activityTypes: _state.activityTypes,
                      fixedContributionsIP: _state.fixedContributionsIP,
                      fixedContributionsPaymentDate:
                          _state.fixedContributionsPaymentDate,
                      contributionsIP1Percent: _state.contributionsIP1Percent,
                      contributionsIP1PercentPaymentDate:
                          _state.contributionsIP1PercentPaymentDate,
                      fixedContributionsDateCtrl:
                          _controllers.fixedContributionsDateCtrl,
                      fixedContributionsDateKey:
                          _controllers.fixedContributionsDateKey,
                      fixedContributionsDateFN:
                          _controllers.fixedContributionsDateFN,
                      contributionsIP1PercentDateCtrl:
                          _controllers.contributionsIP1PercentDateCtrl,
                      contributionsIP1PercentDateKey:
                          _controllers.contributionsIP1PercentDateKey,
                      contributionsIP1PercentDateFN:
                          _controllers.contributionsIP1PercentDateFN,
                      onAddTag:
                          (tag) =>
                              setState(() => _state.additionalTags.add(tag)),
                      onRemoveTag:
                          (tag) =>
                              setState(() => _state.additionalTags.remove(tag)),
                      onActivityTypeAdd:
                          (type) =>
                              setState(() => _state.activityTypes.add(type)),
                      onActivityTypeRemove:
                          (type) =>
                              setState(() => _state.activityTypes.remove(type)),
                      onFixedContributionAdd:
                          (type) => setState(
                            () => _state.fixedContributionsIP.add(type),
                          ),
                      onFixedContributionRemove: (type) {
                        setState(() {
                          _state.fixedContributionsIP.remove(type);
                          _state.fixedContributionsPaymentDate.clear();
                          _controllers.fixedContributionsDateCtrl.clear();
                        });
                      },
                      onFixedContributionDateChanged: (dates) {
                        setState(() {
                          _state.fixedContributionsPaymentDate = dates;
                          _controllers
                              .fixedContributionsDateCtrl
                              .text = ClientConstants.formatPaymentDates(dates);
                        });
                      },
                      onContributionsIP1PercentAdd:
                          (type) => setState(
                            () => _state.contributionsIP1Percent.add(type),
                          ),
                      onContributionsIP1PercentRemove: (type) {
                        setState(() {
                          _state.contributionsIP1Percent.remove(type);
                          _state.contributionsIP1PercentPaymentDate.clear();
                          _controllers.contributionsIP1PercentDateCtrl.clear();
                        });
                      },
                      onContributionsIP1PercentDateChanged: (dates) {
                        setState(() {
                          _state.contributionsIP1PercentPaymentDate = dates;
                          _controllers
                              .contributionsIP1PercentDateCtrl
                              .text = ClientConstants.formatPaymentDates(dates);
                        });
                      },
                      copyToClipboard:
                          (text, field) =>
                              ClientUtils.copyToClipboard(context, text, field),
                    ),
                    const SizedBox(height: ClientConstants.sectionSpacing),

                    PatentsSection(
                      isEditing: _state.isEditing,
                      patents: _state.patents,
                      onUpdatePatent:
                          (index, patent) =>
                              setState(() => _state.patents[index] = patent),
                      onRemovePatent:
                          (index) =>
                              setState(() => _state.patents.removeAt(index)),
                      onAddPatent:
                          () => setState(() {
                            final startDate = DateTime.now();
                            _state.patents.add(
                              PatentEntity(
                                startDate: startDate,
                                endDate: DateTime(
                                  startDate.year + 1,
                                  startDate.month,
                                  startDate.day,
                                ),
                                issueDate: startDate,
                                patentAmount: 0,
                                patentNumber:
                                    'Новый патент ${DateTime.now().millisecondsSinceEpoch}',
                                patentTitle: 'Новый патент',
                              ),
                            );
                          }),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (_state.isEditing) ...[
                          TextButton(
                            onPressed:
                                () => setState(() => _state.isEditing = false),
                            child: const Text('Отмена'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _saveClient,
                            child: const Text('Сохранить'),
                          ),
                        ] else ...[
                          TextButton(
                            onPressed: widget.onCancel,
                            child: const Text('Закрыть'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed:
                                () => setState(() => _state.isEditing = true),
                            child: const Text('Редактировать'),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            right: 8,
            top: 8,
            child: IconButton(
              icon: const Icon(Icons.close),
              onPressed: widget.onCancel,
            ),
          ),
        ],
      ),
    );
  }

  void _saveClient() {
    // Parse dates from text fields before validation
    if (_controllers.creationDateCtrl.text.isNotEmpty) {
      try {
        _state.creationDate = DateFormat(
          'dd.MM.yyyy',
        ).parseStrict(_controllers.creationDateCtrl.text);
      } catch (_) {
        _state.creationDate = ClientUtils.tryParseDate(
          _controllers.creationDateCtrl.text,
        );
        if (_state.creationDate != null) {
          _controllers.creationDateCtrl.text = DateFormat(
            'dd.MM.yyyy',
          ).format(_state.creationDate!);
        }
      }
    } else {
      _state.creationDate = null;
    }

    if (_state.fixedContributionsIP.isNotEmpty &&
        _controllers.fixedContributionsDateCtrl.text.isNotEmpty) {
      try {
        _state
            .fixedContributionsPaymentDate = ClientConstants.parsePaymentDates(
          _controllers.fixedContributionsDateCtrl.text,
          _state.fixedContributionsIP.first,
        );
      } catch (e) {
        // Validator will show the error
      }
    }

    if (_state.contributionsIP1Percent.isNotEmpty &&
        _controllers.contributionsIP1PercentDateCtrl.text.isNotEmpty) {
      try {
        _state.contributionsIP1PercentPaymentDate =
            ClientConstants.parsePaymentDates(
              _controllers.contributionsIP1PercentDateCtrl.text,
              _state.contributionsIP1Percent.first,
            );
      } catch (e) {
        // Validator will show the error
      }
    }

    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Пожалуйста, исправьте ошибки в форме')),
      );

      final fieldsToScroll = [
        _controllers.creationDateKey,
        _controllers.fixedContributionsDateKey,
        _controllers.contributionsIP1PercentDateKey,
        ClientValidationUtils.getInvalidPaymentScheduleKey(
          _state.salaryPayment,
          _state.advancePayment,
          _state.ndflPayment,
          _controllers.salaryPaymentKey,
          _controllers.advancePaymentKey,
          _controllers.ndflPaymentKey,
        ),
        _controllers.enpFieldKey,
      ];

      ClientValidationUtils.scrollToFirstInvalidField(fieldsToScroll);
      return;
    }

    final activeFirm = context.read<ActiveFirmCubit>().state.selectedFirm;
    if (activeFirm == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Сначала выберите фирму')));
      return;
    }

    final newClient = _state.createClientEntity(
      widget.client.id,
      _controllers.nameCtrl.text.trim(),
      _controllers.innCtrl.text.trim().isEmpty
          ? null
          : _controllers.innCtrl.text.trim(),
      _controllers.kppCtrl.text.trim().isEmpty
          ? null
          : _controllers.kppCtrl.text.trim(),
      _controllers.shortNameCtrl.text.trim().isEmpty
          ? null
          : _controllers.shortNameCtrl.text.trim(),
      _controllers.commentCtrl.text.trim().isEmpty
          ? null
          : _controllers.commentCtrl.text.trim(),
    );

    widget.onSaved(newClient);
    setState(() => _state.isEditing = false);
  }
}
