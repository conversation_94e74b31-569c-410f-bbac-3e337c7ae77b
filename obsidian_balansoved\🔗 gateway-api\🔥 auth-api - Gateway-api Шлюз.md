Идентификатор - d5dq1774ef3d58c82i0p
Имя - auth-api
Служебный домен - https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net

---
### Спецификация

```yaml
openapi: 3.0.0
info:
  title: Authentication API
  version: 1.0.0

# НОВОЕ: Глобальная конфигурация API Gateway, включая CORS
# Эта секция применяется ко всему API сразу.
x-yc-apigateway:
  cors:
    # ИСПРАВЛЕНО: Задаем разрешенный источник (origin)
    origin: "*" # Для разработки. В продакшене укажите ваш домен
    
    # ИСПРАВЛЕНО: Синтаксис списка методов с использованием дефисов
    methods:
      - "POST"
      - "OPTIONS"
      
    # ИСПРАВЛЕНО: Синтаксис списка заголовков с использованием дефисов
    allowedHeaders:
      - "Content-Type"
      - "Authorization"
      
    allowCredentials: true
    maxAge: 3600

servers:
- url: https://d5dq1774ef3d58c82i0p.laqt4bj7.apigw.yandexcloud.net

paths:
  # УДАЛЕНО: Индивидуальные блоки x-yc-apigateway-cors из каждого пути,
  # так как теперь работает глобальная настройка.
  
  /register-request:
    post:
      summary: Запросить код для регистрации нового пользователя
      operationId: registerRequest
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email: { type: string }
                password: { type: string }
                user_name: { type: string }
              required: [email, password, user_name]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4er76v5l270502p7qu2
        service_account_id: ajeqgf1b9i412b1cqngu

  /register-confirm:
    post:
      summary: Подтвердить регистрацию с помощью кода
      operationId: registerConfirm
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email: { type: string }
                code: { type: string }
              required: [email, code]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4eu095gn2tga52no22p
        service_account_id: ajeqgf1b9i412b1cqngu

  /login:
    post:
      summary: Войти в систему и получить/вернуть существующий токен
      operationId: login
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email: { type: string }
                password: { type: string }
              required: [email, password]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4e11h046o2u1eihlgf0
        service_account_id: ajeqgf1b9i412b1cqngu

  /refresh-token:
    post:
      summary: Принудительно пересоздать и получить новый токен
      operationId: refreshToken
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email: { type: string }
                password: { type: string }
              required: [email, password]
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4elom5l31834a68a8r7
        service_account_id: ajeqgf1b9i412b1cqngu
```