part of 'clients_cubit.dart';

class ClientsState extends Equatable {
  final List<ClientEntity> clients;
  final bool isLoading;
  final String? error;

  const ClientsState({
    required this.clients,
    required this.isLoading,
    this.error,
  });

  const ClientsState.initial() : this(clients: const [], isLoading: false);

  ClientsState copyWith({
    List<ClientEntity>? clients,
    bool? isLoading,
    String? error,
  }) {
    return ClientsState(
      clients: clients ?? this.clients,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  List<Object?> get props => [clients, isLoading, error];
}
