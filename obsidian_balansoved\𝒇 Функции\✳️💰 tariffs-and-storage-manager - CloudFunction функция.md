Идентификатор - d4ek9ojgjnoibdsi1qut
Описание - 💰 Управляет тарифами, квотами и файлами в Object Storage.
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя.
	-> Тело запроса:
		- `action` (string, **обязательно**): Тип операции.
		- `firm_id` (string, **обязательно**): ID фирмы.
		- **Для `GET_RECORD`**:
			- Никаких доп. полей.
		- **Для `UPDATE_JSON`**:
			- `target_json_field` (string): Имя JSON-поля для обновления (`subscription_info_json`, `storage_info_json`, `confidential_data_json`).
			- `updates` (object): Словарь с ключами и значениями для обновления.
		- **Для `CLEAR_JSON`**:
			- `fields_to_clear` (list): Список JSON-полей для очистки.
		- **Для `GET_UPLOAD_URL`**:
			- `filename` (string): Имя файла.
			- `filesize` (integer): Размер файла в байтах.
		- **Для `DELETE_FILE`**:
			- `file_key` (string): Ключ файла в S3.

Внутренняя работа:
1.  **Авторизация**: Проверяется JWT токен, извлекается `user_id`.
2.  **Парсинг запроса**: Используется `request_parser` для извлечения данных.
3.  **Проверка прав**: Для всех операций проверяется членство пользователя в `firm_id`. Для `GET_RECORD`, `UPDATE_JSON`, `CLEAR_JSON` дополнительно требуются права `OWNER` или `ADMIN`.
4.  **Маршрутизация по `action`**:
    -   **`GET_RECORD`**: Вызывает `get_logic.get_or_create_record`. Если записи для фирмы нет, создает ее с параметрами по умолчанию (квота хранилища 100 МБ).
    -   **`UPDATE_JSON`**: Вызывает `update_logic.update_json_fields`. Атомарно (чтение-изменение-запись) обновляет ключи в указанном JSON-поле.
    -   **`CLEAR_JSON`**: Вызывает `update_logic.clear_json_fields`. Устанавливает для указанных JSON-полей значение `{}`.
    -   **`GET_UPLOAD_URL`**: Вызывает `storage_logic.handle_get_upload_url`. Проверяет квоту хранилища из `subscription_info_json` и текущее использование из `storage_info_json`, затем генерирует pre-signed URL.
    -   **`DELETE_FILE`**: Вызывает `storage_logic.handle_delete_file`. Удаляет файл из S3, получает его размер и атомарно уменьшает значение `used_bytes` в `storage_info_json`.

На выходе:
-   `200 OK` (GET_RECORD): `{"data": {...}}`
-   `200 OK` (UPDATE_JSON/CLEAR_JSON): `{"message": "..."}`
-   `200 OK` (GET_UPLOAD_URL): `{"upload_url": "...", "file_key": "..."}`
-   `200 OK` (DELETE_FILE): `{"message": "File deleted"}`
-   `400 Bad Request`, `403 Forbidden`, `404 Not Found`, `413 Payload Too Large`.

---
#### Зависимости и окружение
-   **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`, `utils/storage_utils.py`
-   **Переменные окружения**:
    -   `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS` ([[💾 firms-database - База данных YandexDatabase]])
    -   `YDB_ENDPOINT_TARIFFS`, `YDB_DATABASE_TARIFFS` ([[💾 tariffs-and-storage-database - База данных YandexDatabase]])
    -   `SA_KEY_FILE` ([[ydb_sa_key.json]])
    -   `JWT_SECRET`
    -   `STORAGE_BUCKET_NAME`
    -   `STORAGE_ENDPOINT` ("https://storage.yandexcloud.net")
    -   `STORAGE_REGION` ("ru-central1")
    -   `STORAGE_ACCESS_KEY` ([[🗝️ auth-service-acc - Статический ключ доступа]])
    -   `STORAGE_SECRET_KEY` ([[🗝️ auth-service-acc - Статический ключ доступа]])

---
#### index.py
```python
# index.py

import json, os, logging, sys
import ydb
from utils import auth_utils, ydb_utils, request_parser
import get_logic
import update_logic
import storage_logic
from custom_errors import AuthError, LogicError, NotFoundError, QuotaExceededError

# Устанавливаем уровень логирования
logging.getLogger().setLevel(logging.INFO)

def check_permissions(session, user_id, firm_id):
    logging.info(f"Checking permissions for user_id: {user_id} in firm_id: {firm_id}")
    query_text = "DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;"
    query = session.prepare(query_text)
    result = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$user_id": user_id, "$firm_id": firm_id}, commit_tx=True)
    if not result[0].rows:
        logging.warning(f"Permission check failed: User {user_id} is not a member of firm {firm_id}.")
        return (False, False)
    roles = json.loads(result[0].rows[0].roles)
    is_admin_or_owner = "OWNER" in roles or "ADMIN" in roles
    logging.info(f"Permission check successful: User is member, is_admin_or_owner={is_admin_or_owner}.")
    return (True, is_admin_or_owner)

def handler(event, context):
    logging.info("--- NEW INVOCATION ---")
    
    # Очищаем кеш драйверов в начале каждого вызова для предотвращения ошибок с "протухшими" соединениями
    if 'ydb_utils' in sys.modules and hasattr(sys.modules['ydb_utils'], 'clear_drivers_cache'):
        ydb_utils.clear_drivers_cache()
    
    logging.info(f"RAW EVENT: {event}")
    try:
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized: Missing or invalid Bearer token format.")
        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid or expired token.")
        
        requesting_user_id = user_payload['user_id']
        logging.info(f"Request authorized for user_id: {requesting_user_id}")
        
        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        action = data.get('action')
        logging.info(f"Parsed action: '{action}' for firm_id: '{firm_id}'")
        
        if not all([firm_id, action]):
            raise LogicError("firm_id and action are required parameters.")

        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)
        is_member, is_admin_or_owner = firms_pool.retry_operation_sync(
            lambda s: check_permissions(s, requesting_user_id, firm_id)
        )

        if not is_member:
            raise AuthError("User is not a member of the specified firm.")

        tariffs_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_TARIFFS"], os.environ["YDB_DATABASE_TARIFFS"])
        tariffs_pool = ydb.SessionPool(tariffs_driver)
        logging.info(f"Successfully connected to DB pools. Routing action '{action}'...")

        if action == 'GET_RECORD':
            if not is_admin_or_owner: raise AuthError("Admin or Owner rights required for GET_RECORD.")
            return tariffs_pool.retry_operation_sync(lambda s: get_logic.get_or_create_record(s, firm_id))

        elif action == 'UPDATE_JSON':
            if not is_admin_or_owner: raise AuthError("Admin or Owner rights required for UPDATE_JSON.")
            target_field = data.get('target_json_field')
            updates = data.get('updates')
            return tariffs_pool.retry_operation_sync(lambda s: update_logic.update_json_fields(s, firm_id, target_field, updates))

        elif action == 'CLEAR_JSON':
            if not is_admin_or_owner: raise AuthError("Admin or Owner rights required for CLEAR_JSON.")
            fields = data.get('fields_to_clear')
            return tariffs_pool.retry_operation_sync(lambda s: update_logic.clear_json_fields(s, firm_id, fields))
        
        elif action == 'GET_UPLOAD_URL':
            filename = data.get('filename')
            filesize = data.get('filesize')
            return storage_logic.handle_get_upload_url(tariffs_pool, firm_id, filename, filesize)

        elif action == 'GET_DOWNLOAD_URL':
            file_key = data.get('file_key')
            return storage_logic.handle_get_download_url(firm_id, file_key)

        elif action == 'CONFIRM_UPLOAD':
            file_key = data.get('file_key')
            return storage_logic.handle_confirm_upload(tariffs_pool, firm_id, file_key)

        elif action == 'DELETE_FILE':
            file_key = data.get('file_key')
            return storage_logic.handle_delete_file(tariffs_pool, firm_id, file_key)

        else:
            raise LogicError(f"Invalid action specified: '{action}'")

    except (AuthError, PermissionError) as e:
        logging.warning(f"Authorization error: {e}", exc_info=True)
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        logging.warning(f"Business logic error: {e}", exc_info=True)
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        logging.warning(f"Not found error: {e}")
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except QuotaExceededError as e:
        logging.warning(f"Quota exceeded error: {e}", exc_info=True)
        return {"statusCode": 413, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Critical unhandled error in handler: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```
#### get_logic.py
```python
import json
import datetime
import pytz
import logging
import ydb  # ИСПРАВЛЕНО: Добавлен недостающий импорт
from custom_errors import NotFoundError

DEFAULT_QUOTA_BYTES = 100 * 1024 * 1024 # 100 MB

def _create_default_record(session, firm_id):
    logging.info(f"Creating default record in DB for firm_id: {firm_id}")
    now = datetime.datetime.now(pytz.utc)
    default_subscription = {"plan_id": "free", "started_at": now.isoformat(), "expires_at": None, "auto_renew": False, "status": "active", "quota_bytes": DEFAULT_QUOTA_BYTES}
    default_storage = {"used_bytes": 0, "last_recalculated_at": now.isoformat()}
    
    query_text = """
        DECLARE $firm_id AS Utf8;
        DECLARE $sub_info AS Json;
        DECLARE $storage_info AS Json;
        DECLARE $conf_data AS Json;
        DECLARE $created_at AS Timestamp;
        DECLARE $updated_at AS Timestamp;
        UPSERT INTO `tariffs_and_storage` (firm_id, subscription_info_json, storage_info_json, confidential_data_json, created_at, updated_at)
        VALUES ($firm_id, $sub_info, $storage_info, $conf_data, $created_at, $updated_at);
    """
    params = {
        "$firm_id": firm_id,
        "$sub_info": json.dumps(default_subscription),
        "$storage_info": json.dumps(default_storage),
        "$conf_data": json.dumps({}),
        "$created_at": now,
        "$updated_at": now
    }
    try:
        session.transaction(ydb.SerializableReadWrite()).execute(session.prepare(query_text), params, commit_tx=True)
        logging.info(f"Default record for firm {firm_id} created successfully.")
    except Exception as e:
        logging.error(f"Failed to create default record for firm {firm_id}: {e}", exc_info=True)
        raise
    
    return {
        "firm_id": firm_id,
        "subscription_info_json": default_subscription,
        "storage_info_json": default_storage,
        "confidential_data_json": {},
        "created_at": now,
        "updated_at": now
    }

def get_or_create_record(session, firm_id):
    logging.info(f"Attempting to get or create record for firm_id: {firm_id}")
    query = session.prepare("DECLARE $firm_id AS Utf8; SELECT * FROM `tariffs_and_storage` WHERE firm_id = $firm_id;")
    res = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$firm_id": firm_id}, commit_tx=True)

    if res[0].rows:
        logging.info(f"Record for firm {firm_id} found in DB.")
        row = res[0].rows[0]
        data = {c.name: (json.loads(row[c.name]) if 'json' in c.name and row[c.name] else ({} if 'json' in c.name else row[c.name])) for c in res[0].columns}
    else:
        logging.warning(f"Record for firm {firm_id} not found. Proceeding to create a default one.")
        data = _create_default_record(session, firm_id)

    return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}
```
#### update_logic.py
```python
import json
import datetime
import pytz
import logging
import ydb  # ИСПРАВЛЕНО: Добавлен недостающий импорт
from custom_errors import LogicError, NotFoundError

VALID_JSON_FIELDS = {"subscription_info_json", "storage_info_json", "confidential_data_json"}

def update_json_fields(session, firm_id, target_json_field, updates):
    logging.info(f"Attempting to update field '{target_json_field}' for firm {firm_id}")
    if not target_json_field or target_json_field not in VALID_JSON_FIELDS:
        raise LogicError(f"Invalid 'target_json_field'. Must be one of {VALID_JSON_FIELDS}")
    if not isinstance(updates, dict):
        raise LogicError("'updates' must be a JSON object.")

    tx = session.transaction(ydb.SerializableReadWrite())
    
    logging.info("Step 1: Reading current record from DB.")
    read_query = session.prepare(f"DECLARE $firm_id AS Utf8; SELECT {target_json_field} FROM `tariffs_and_storage` WHERE firm_id = $firm_id;")
    res = tx.execute(read_query, {"$firm_id": firm_id})
    if not res[0].rows:
        raise NotFoundError(f"Record for firm_id {firm_id} not found. Cannot update.")
    
    current_json_str = res[0].rows[0][target_json_field]
    current_data = json.loads(current_json_str or '{}')
    logging.info(f"Current data: {current_data}")
    
    logging.info(f"Step 2: Modifying data with updates: {updates}")
    current_data.update(updates)
    new_json_str = json.dumps(current_data)
    logging.info(f"New data to be written: {new_json_str}")
    
    logging.info("Step 3: Writing updated record to DB.")
    now = datetime.datetime.now(pytz.utc)
    update_query = session.prepare(f"""
        DECLARE $firm_id AS Utf8;
        DECLARE $new_json AS Json;
        DECLARE $now AS Timestamp;
        UPDATE `tariffs_and_storage` SET {target_json_field} = $new_json, updated_at = $now WHERE firm_id = $firm_id;
    """)
    tx.execute(update_query, {"$firm_id": firm_id, "$new_json": new_json_str, "$now": now})
    
    tx.commit()
    logging.info(f"Field '{target_json_field}' for firm {firm_id} updated successfully.")
    return {"statusCode": 200, "body": json.dumps({"message": f"Field '{target_json_field}' updated successfully."})}

def clear_json_fields(session, firm_id, fields_to_clear):
    logging.info(f"Attempting to clear fields {fields_to_clear} for firm {firm_id}")
    if not isinstance(fields_to_clear, list):
        raise LogicError("'fields_to_clear' must be a list of field names.")
    
    set_clauses = []
    for field in fields_to_clear:
        if field in VALID_JSON_FIELDS:
            set_clauses.append(f"{field} = CAST('{{}}' AS Json)")
        else:
            logging.warning(f"Invalid field name '{field}' found in list to clear. Ignoring.")
            
    if not set_clauses:
        raise LogicError("No valid fields provided to clear.")

    tx = session.transaction(ydb.SerializableReadWrite())
    now = datetime.datetime.now(pytz.utc)
    
    update_query_text = f"""
        DECLARE $firm_id AS Utf8;
        DECLARE $now AS Timestamp;
        UPDATE `tariffs_and_storage` SET {', '.join(set_clauses)}, updated_at = $now WHERE firm_id = $firm_id;
    """
    tx.execute(session.prepare(update_query_text), {"$firm_id": firm_id, "$now": now})
    
    tx.commit()
    logging.info(f"Fields {fields_to_clear} cleared for firm {firm_id}.")
    return {"statusCode": 200, "body": json.dumps({"message": f"Fields {fields_to_clear} cleared successfully."})}
```
#### storage_logic.py
```python
# storage_logic.py

import os
import re
import datetime
import json
import logging
import pytz
import ydb
from utils import storage_utils
from custom_errors import LogicError, QuotaExceededError, NotFoundError, AuthError
import get_logic

def handle_get_upload_url(pool, firm_id, filename, filesize):
    logging.info(f"Handling GET_UPLOAD_URL for firm {firm_id}, filename: {filename}, size: {filesize}")
    if not all([filename, filesize]):
        raise LogicError("filename and filesize are required for GET_UPLOAD_URL.")
    if not isinstance(filesize, int) or filesize <= 0:
        raise LogicError("filesize must be a positive integer.")

    # Шаг 1: Получаем актуальный размер использованного пространства из Object Storage
    logging.info("Calculating actual storage usage from S3.")
    s3_client = storage_utils.get_s3_client()
    bucket_name = os.environ['STORAGE_BUCKET_NAME']
    try:
        prefix = f"{firm_id}/"
        actual_used_bytes = storage_utils.get_folder_size(s3_client, bucket_name, prefix)
    except Exception:
        logging.error(f"Could not calculate storage size for firm {firm_id}. Aborting.")
        raise Exception("Could not verify storage quota due to an internal error.")

    # Шаг 2: Получаем запись из БД, чтобы узнать квоту и сравнить с актуальным использованием
    logging.info("Fetching current tariff and storage info from DB.")
    record_response = pool.retry_operation_sync(lambda s: get_logic.get_or_create_record(s, firm_id))
    record_data = json.loads(record_response['body'])['data']
    
    quota_bytes = record_data.get('subscription_info_json', {}).get('quota_bytes', 0)
    used_bytes_from_db = record_data.get('storage_info_json', {}).get('used_bytes', 0)
    
    logging.info(f"Real-time Quota Check for firm {firm_id}: ActualUsed={actual_used_bytes}, FileSize={filesize}, Quota={quota_bytes}")

    # Шаг 3: Проверяем квоту, используя актуальные данные
    if (actual_used_bytes + filesize) > quota_bytes:
        raise QuotaExceededError(f"Upload failed: storage quota will be exceeded. Used: {actual_used_bytes}, File: {filesize}, Quota: {quota_bytes}")

    # Шаг 4: (Опционально, но рекомендуется) Если данные в БД устарели, синхронизируем их
    if actual_used_bytes != used_bytes_from_db:
        logging.warning(f"DB usage is out of sync for firm {firm_id}. DB: {used_bytes_from_db}, Real: {actual_used_bytes}. Syncing...")
        def sync_transaction(session):
            storage_info = record_data.get('storage_info_json', {})
            storage_info['used_bytes'] = actual_used_bytes
            storage_info['last_recalculated_at'] = datetime.datetime.now(pytz.utc).isoformat()
            
            update_q = session.prepare("DECLARE $fid AS Utf8; DECLARE $sij AS Json; UPDATE `tariffs_and_storage` SET storage_info_json = $sij WHERE firm_id = $fid;")
            tx = session.transaction(ydb.SerializableReadWrite())
            tx.execute(update_q, {"$fid": firm_id, "$sij": json.dumps(storage_info)})
            tx.commit()
            logging.info(f"Successfully synced used_bytes for firm {firm_id} to {actual_used_bytes}.")
        
        pool.retry_operation_sync(sync_transaction)

    # Шаг 5: Если проверка квоты пройдена, генерируем ссылку для загрузки
    logging.info("Quota check passed. Generating file key and presigned URL.")
    file_key, upload_url = storage_utils.generate_upload_artefacts(firm_id, filename)

    if not upload_url or not file_key:
        logging.error("storage_utils.generate_upload_artefacts failed to return artefacts.")
        raise Exception("Could not generate an upload URL from the storage service.")

    logging.info(f"Successfully generated upload URL for file_key: {file_key}")
    return {"statusCode": 200, "body": json.dumps({"upload_url": upload_url, "file_key": file_key})}


def handle_delete_file(pool, firm_id, file_key):
    logging.info(f"Handling DELETE_FILE for firm {firm_id}, file_key: {file_key}")
    if not file_key:
        raise LogicError("file_key is required for DELETE action.")
    if not file_key.startswith(f"{firm_id}/"):
        raise AuthError("Permission denied: you are not allowed to access this file key.")

    s3_client = storage_utils.get_s3_client()
    bucket_name = os.environ['STORAGE_BUCKET_NAME']
    
    try:
        logging.info(f"Checking existence and getting metadata for S3 object: {file_key}")
        head_response = s3_client.head_object(Bucket=bucket_name, Key=file_key)
        file_size = head_response['ContentLength']
        logging.info(f"Object found. Size is {file_size} bytes.")
    except s3_client.exceptions.ClientError as e:
        if e.response['Error']['Code'] == '404':
            logging.warning(f"File with key {file_key} not found in storage. Cannot delete.")
            raise NotFoundError(f"File with key {file_key} not found.")
        else:
            logging.error(f"S3 ClientError when checking file {file_key}: {e}", exc_info=True)
            raise

    logging.info(f"Attempting to delete S3 object: {file_key}")
    storage_utils.delete_object(s3_client, bucket_name, file_key)
    logging.info(f"S3 object {file_key} deleted successfully.")

    if file_size > 0:
        logging.info(f"Updating used_bytes in DB by decrementing {file_size} bytes.")
        def decrement_storage_size_transaction(session):
            tx = session.transaction(ydb.SerializableReadWrite())
            read_q = session.prepare("DECLARE $fid AS Utf8; SELECT storage_info_json FROM `tariffs_and_storage` WHERE firm_id = $fid;")
            res = tx.execute(read_q, {"$fid": firm_id})
            if not res[0].rows:
                logging.warning(f"No record found for firm {firm_id} in tariffs DB to decrement storage size.")
                return

            storage_info = json.loads(res[0].rows[0].storage_info_json or '{}')
            current_used = storage_info.get('used_bytes', 0)
            logging.info(f"Current used_bytes: {current_used}. New used_bytes will be {max(0, current_used - file_size)}.")
            storage_info['used_bytes'] = max(0, current_used - file_size)
            
            update_q = session.prepare("DECLARE $fid AS Utf8; DECLARE $sij AS Json; UPDATE `tariffs_and_storage` SET storage_info_json = $sij WHERE firm_id = $fid;")
            tx.execute(update_q, {"$fid": firm_id, "$sij": json.dumps(storage_info)})
            tx.commit()
            logging.info(f"Successfully updated used_bytes for firm {firm_id}.")
        
        pool.retry_operation_sync(decrement_storage_size_transaction)

    return {"statusCode": 200, "body": json.dumps({"message": "File deleted successfully"})}

def handle_get_download_url(firm_id, file_key):
    """
    Обрабатывает запрос на получение ссылки для скачивания.
    """
    logging.info(f"Handling GET_DOWNLOAD_URL for firm {firm_id}, file_key: {file_key}")
    if not file_key:
        raise LogicError("file_key is required for GET_DOWNLOAD_URL action.")
    
    if not file_key.startswith(f"{firm_id}/"):
        raise AuthError("Permission denied: you are not allowed to access this file key.")

    s3_client = storage_utils.get_s3_client()
    bucket_name = os.environ['STORAGE_BUCKET_NAME']

    try:
        s3_client.head_object(Bucket=bucket_name, Key=file_key)
    except s3_client.exceptions.ClientError as e:
        if e.response['Error']['Code'] == '404':
            raise NotFoundError(f"Cannot get download URL. File with key {file_key} not found.")
        else:
            raise

    download_url = storage_utils.generate_presigned_download_url(s3_client, bucket_name, file_key)

    if not download_url:
        raise Exception("Could not generate a download URL from the storage service.")

    logging.info(f"Successfully generated download URL for file_key: {file_key}")
    return {"statusCode": 200, "body": json.dumps({"download_url": download_url, "file_key": file_key})}

def handle_confirm_upload(pool, firm_id, file_key):
    """
    Подтверждает успешную загрузку файла, получает его размер из S3
    и атомарно увеличивает счетчик used_bytes в базе данных.
    """
    logging.info(f"Handling CONFIRM_UPLOAD for firm {firm_id}, file_key: {file_key}")
    if not file_key:
        raise LogicError("file_key is required for CONFIRM_UPLOAD action.")
    if not file_key.startswith(f"{firm_id}/"):
        raise AuthError("Permission denied: you are not allowed to access this file key.")

    s3_client = storage_utils.get_s3_client()
    bucket_name = os.environ['STORAGE_BUCKET_NAME']
    
    file_size = 0
    try:
        logging.info(f"Getting metadata for S3 object: {file_key}")
        file_size = s3_client.head_object(Bucket=bucket_name, Key=file_key)['ContentLength']
        logging.info(f"Object size is {file_size} bytes.")
    except s3_client.exceptions.ClientError as e:
        if e.response['Error']['Code'] == '404':
            raise NotFoundError(f"Cannot confirm upload. File with key {file_key} not found in storage.")
        else:
            raise

    if file_size > 0:
        logging.info(f"Updating used_bytes in DB by incrementing {file_size} bytes.")
        
        def increment_storage_size_transaction(session):
            tx = session.transaction(ydb.SerializableReadWrite())
            # Сначала читаем текущее значение
            read_q = session.prepare("DECLARE $fid AS Utf8; SELECT storage_info_json FROM `tariffs_and_storage` WHERE firm_id = $fid;")
            res = tx.execute(read_q, {"$fid": firm_id})
            if not res[0].rows:
                logging.warning(f"No record found for firm {firm_id} in tariffs DB to increment storage size.")
                return

            storage_info = json.loads(res[0].rows[0].storage_info_json or '{}')
            current_used = storage_info.get('used_bytes', 0)
            logging.info(f"Current used_bytes: {current_used}. New used_bytes will be {current_used + file_size}.")
            storage_info['used_bytes'] = current_used + file_size
            
            # Затем записываем новое
            update_q = session.prepare("DECLARE $fid AS Utf8; DECLARE $sij AS Json; UPDATE `tariffs_and_storage` SET storage_info_json = $sij WHERE firm_id = $fid;")
            tx.execute(update_q, {"$fid": firm_id, "$sij": json.dumps(storage_info)})
            tx.commit()
            logging.info(f"Successfully updated used_bytes for firm {firm_id}.")
        
        pool.retry_operation_sync(increment_storage_size_transaction)

    return {"statusCode": 200, "body": json.dumps({"message": "Upload confirmed and storage usage updated."})}
```
#### custom_errors.py
```python
class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass
class QuotaExceededError(Exception): pass
```