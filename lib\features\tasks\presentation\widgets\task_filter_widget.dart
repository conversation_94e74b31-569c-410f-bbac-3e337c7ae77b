import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/task_create/form_sections.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_picker_dialog.dart';

class TaskFilterWidget extends StatefulWidget {
  final TaskRequestParams currentParams;
  final TaskPaginationMeta? paginationMeta;
  final ValueChanged<TaskRequestParams> onParamsChanged;
  final VoidCallback? onNextPage;
  final VoidCallback? onPreviousPage;

  const TaskFilterWidget({
    super.key,
    required this.currentParams,
    this.paginationMeta,
    required this.onParamsChanged,
    this.onNextPage,
    this.onPreviousPage,
  });

  @override
  State<TaskFilterWidget> createState() => _TaskFilterWidgetState();
}

class _TaskFilterWidgetState extends State<TaskFilterWidget> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ClientsCubit, ClientsState>(
      builder: (context, clientsState) {
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: () => setState(() => _isExpanded = !_isExpanded),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          _isExpanded
                              ? 'Настройка фильтра'
                              : _buildSummaryTitle(context, clientsState),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                    ],
                  ),
                ),
              ),
              if (_isExpanded) _buildCollapsibleBody(context),
            ],
          ),
        );
      },
    );
  }

  String _buildSummaryTitle(BuildContext context, ClientsState clientsState) {
    final params = widget.currentParams;
    final typeStr =
        params.viewType == TaskViewType.timeless
            ? 'Бессрочные задачи'
            : 'Задачи за ${_formatMonth(DateTime(params.year!, params.month!))}';

    String clientStr = 'все клиенты';
    if (params.clientId != null) {
      try {
        final client = clientsState.clients.firstWhere(
          (c) => c.id == params.clientId,
        );
        clientStr = 'клиент: ${client.name}';
      } catch (e) {
        clientStr = 'загрузка клиента...';
      }
    }

    return 'Фильтр: $typeStr ($clientStr)';
  }

  Widget _buildCollapsibleBody(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Тип задач',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          SegmentedButton<TaskViewType>(
            segments: const [
              ButtonSegment<TaskViewType>(
                value: TaskViewType.timeless,
                label: Text('Бессрочные'),
                icon: Icon(Icons.all_inbox),
              ),
              ButtonSegment<TaskViewType>(
                value: TaskViewType.dated,
                label: Text('С крайним сроком'),
                icon: Icon(Icons.schedule),
              ),
            ],
            selected: {widget.currentParams.viewType},
            onSelectionChanged: (Set<TaskViewType> selection) {
              final newType = selection.first;
              if (newType == TaskViewType.timeless) {
                widget.onParamsChanged(
                  TaskRequestParams.timeless(
                    clientId: widget.currentParams.clientId,
                  ),
                );
              } else {
                final now = DateTime.now();
                widget.onParamsChanged(
                  TaskRequestParams.dated(
                    month: now.month,
                    year: now.year,
                    clientId: widget.currentParams.clientId,
                  ),
                );
              }
            },
          ),
          const SizedBox(height: 16),
          ClientSelector(
            selectedClientIds:
                widget.currentParams.clientId == null
                    ? []
                    : [widget.currentParams.clientId!],
            onClientChanged: (clientId, selected) {
              final newClientId = selected ? clientId : null;
              widget.onParamsChanged(
                widget.currentParams.copyWith(clientId: newClientId),
              );
            },
          ),
          const SizedBox(height: 16),
          if (widget.currentParams.viewType == TaskViewType.timeless)
            _buildTimelessControls(context)
          else
            _buildDatedControls(context),
        ],
      ),
    );
  }

  Widget _buildTimelessControls(BuildContext context) {
    final meta = widget.paginationMeta;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Бессрочные задачи', style: Theme.of(context).textTheme.bodySmall),
        if (meta != null) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Стр. ${meta.currentPage + 1} из ${meta.totalPages} (всего: ${meta.totalTasks})',
                  style: const TextStyle(fontSize: 13),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: meta.hasPreviousPage ? widget.onPreviousPage : null,
                tooltip: 'Предыдущая страница',
              ),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                onPressed: meta.hasNextPage ? widget.onNextPage : null,
                tooltip: 'Следующая страница',
              ),
            ],
          ),
        ] else ...[
          const SizedBox(height: 8),
          const Text(
            'Загрузка...',
            style: TextStyle(fontSize: 13, color: Colors.grey),
          ),
        ],
      ],
    );
  }

  Widget _buildDatedControls(BuildContext context) {
    final params = widget.currentParams;
    final selectedDate = DateTime(params.year!, params.month!);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Задачи с крайним сроком',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            IconButton(
              icon: const Icon(Icons.chevron_left),
              onPressed: () {
                final newDate = DateTime(
                  selectedDate.year,
                  selectedDate.month - 1,
                );
                widget.onParamsChanged(
                  params.copyWith(month: newDate.month, year: newDate.year),
                );
              },
              tooltip: 'Предыдущий месяц',
            ),
            Expanded(
              child: GestureDetector(
                onTap: () async {
                  final pickedDate = await SmartDatePickerDialog.show(
                    context: context,
                    initialDate: selectedDate,
                    firstDate: DateTime(2020),
                    lastDate: DateTime(2030),
                    helpText: 'Выберите месяц и год',
                    allowClear: false,
                  );
                  if (pickedDate != null) {
                    widget.onParamsChanged(
                      params.copyWith(
                        month: pickedDate.month,
                        year: pickedDate.year,
                      ),
                    );
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Theme.of(context).dividerColor),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      _formatMonth(selectedDate),
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.chevron_right),
              onPressed: () {
                final newDate = DateTime(
                  selectedDate.year,
                  selectedDate.month + 1,
                );
                widget.onParamsChanged(
                  params.copyWith(month: newDate.month, year: newDate.year),
                );
              },
              tooltip: 'Следующий месяц',
            ),
          ],
        ),
      ],
    );
  }

  String _formatMonth(DateTime date) {
    const months = [
      'Январь',
      'Февраль',
      'Март',
      'Апрель',
      'Май',
      'Июнь',
      'Июль',
      'Август',
      'Сентябрь',
      'Октябрь',
      'Ноябрь',
      'Декабрь',
    ];
    return '${months[date.month - 1]} ${date.year}';
  }
}
