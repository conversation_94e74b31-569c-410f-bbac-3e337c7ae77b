import 'package:flutter/material.dart';
import 'pdf_preview_widget.dart';

class FilePreviewDialog extends StatelessWidget {
  final String fileKey;
  final String fileName;

  const FilePreviewDialog({
    super.key,
    required this.fileKey,
    required this.fileName,
  });

  static void show(BuildContext context, String fileKey, String fileName) {
    final extension = fileName.split('.').last.toLowerCase();

    if (extension == 'pdf') {
      showDialog(
        context: context,
        builder:
            (BuildContext context) =>
                FilePreviewDialog(fileKey: fileKey, fileName: fileName),
      );
    } else if (extension == 'doc' || extension == 'docx') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Предпросмотр для этого типа файла недоступен.'),
          duration: Duration(seconds: 2),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Предпросмотр недоступен для данного типа файла.'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(fileName),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        child: PdfPreviewWidget(fileKey: fileKey, fileName: fileName),
      ),
      actions: <Widget>[
        TextButton(
          child: const Text('Закрыть'),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }
}
