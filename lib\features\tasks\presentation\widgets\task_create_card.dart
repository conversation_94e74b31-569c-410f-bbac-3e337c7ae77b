import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/task_entity.dart';
import '../cubit/tasks_cubit.dart';
import '../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'task_create/models.dart';
import 'task_create/dialogs.dart';
import 'task_create/form_sections.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_picker_dialog.dart';

class TaskCreateCard extends StatefulWidget {
  final TaskEntity? initialTask;
  final void Function(TaskEntity)? onSaved;
  final VoidCallback? onCancel;
  final void Function(TaskEntity)? onCreated;

  const TaskCreateCard({
    super.key,
    this.initialTask,
    this.onSaved,
    this.onCancel,
    this.onCreated,
  });

  @override
  State<TaskCreateCard> createState() => _TaskCreateCardState();
}

class _TaskCreateCardState extends State<TaskCreateCard> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  // Списки выбранных ID
  final List<String> _selectedClientIds = [];
  List<String> _selectedAssigneeIds = [];
  List<String> _selectedCoAssigneeIds = [];
  List<String> _selectedObserverIds = [];
  List<String> _selectedCreatorIds = [];

  // Основные поля
  DateTime? _dueDate;
  String _priority = 'medium';

  // Дополнительные параметры
  bool _allowAssigneeToChangeDueDate = false;

  // Чек-лист
  final List<ChecklistItem> _checklist = [];

  // Напоминания
  final List<ReminderItem> _reminders = [];

  // Файлы (старый формат для совместимости)

  // Облачные файлы
  final List<FileAttachmentItem> _cloudFiles = [];

  // Повторение
  String _recurrenceType = 'none';
  String _recurrenceInterval = 'monthly';

  // Перенос праздников
  String _holidayTransferRule = 'next_workday';

  // Состояние файловых операций
  bool get _hasActiveFileOperations {
    return _cloudFiles.any(
      (file) =>
          file.status == FileAttachmentStatus.uploading ||
          file.status == FileAttachmentStatus.deleting ||
          file.status == FileAttachmentStatus.pending,
    );
  }

  // Флаг для отслеживания была ли задача автоматически создана
  bool _wasAutoCreated = false;

  @override
  void initState() {
    super.initState();

    final task = widget.initialTask;
    if (task != null) {
      _titleController.text = task.title;
      _descriptionController.text = task.description ?? '';

      _selectedClientIds.addAll(task.clientIds);
      _selectedAssigneeIds = List<String>.from(
        task.assigneeIds.isNotEmpty ? [task.assigneeIds.first] : [],
      );
      if (task.assigneeIds.length > 1) {
        _selectedCoAssigneeIds = task.assigneeIds.sublist(1);
      }
      _selectedObserverIds = List<String>.from(task.observerIds);
      _selectedCreatorIds = List<String>.from(task.creatorIds);

      _dueDate = task.dueDate;
      _priority = task.priority;

      _allowAssigneeToChangeDueDate =
          task.options['allow_assignee_to_change_due_date'] == true;

      _checklist.addAll(
        task.checklist.map(
          (e) => ChecklistItem(
            text: e['text'] ?? '',
            completed: e['completed'] == true || e['is_done'] == true,
          ),
        ),
      );

      // Загружаем облачные файлы (с ключами)
      final cloudAttachments = task.attachments.where(
        (e) => e['fileKey'] != null,
      );
      _cloudFiles.addAll(
        cloudAttachments.map(
          (e) => FileAttachmentItem(
            name: e['name'] ?? '',
            fileKey: e['fileKey'] as String?,
            fileSize: e['fileSize'] as int?,
            status: FileAttachmentStatus.uploaded,
          ),
        ),
      );

      _reminders.addAll(
        task.reminders.map((e) {
          DateTime? dt;
          final dtRaw = e['datetime'];
          if (dtRaw is String && dtRaw.isNotEmpty) {
            dt = DateTime.tryParse(dtRaw)?.toLocal();
          }
          return ReminderItem(
            dateTime: dt ?? DateTime.now(),
            role: e['role'] ?? 'assignee',
          );
        }),
      );

      if (task.recurrence != null && task.recurrence!.isNotEmpty) {
        _recurrenceType = task.recurrence!['type']?.toString() ?? 'none';
        if (_recurrenceType == 'periodic') {
          _recurrenceInterval =
              task.recurrence!['interval']?.toString() ?? 'monthly';
        }
      }

      _holidayTransferRule = task.holidayTransferRule ?? 'next_workday';
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Заголовок
            Text(
              widget.initialTask == null
                  ? 'Создать задачу'
                  : 'Редактировать задачу',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),

            // Форма
            Flexible(
              fit: FlexFit.loose,
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Название задачи
                      TextFormField(
                        controller: _titleController,
                        decoration: const InputDecoration(
                          labelText: 'Название задачи *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Введите название задачи';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),

                      // Описание
                      TextFormField(
                        controller: _descriptionController,
                        maxLines: 3,
                        decoration: const InputDecoration(
                          labelText: 'Описание',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Клиенты
                      ClientSelector(
                        selectedClientIds: _selectedClientIds,
                        onClientChanged: (clientId, selected) {
                          setState(() {
                            if (selected) {
                              _selectedClientIds.add(clientId);
                            } else {
                              _selectedClientIds.remove(clientId);
                            }
                          });
                        },
                      ),
                      const SizedBox(height: 24),

                      // Исполнители
                      EmployeeSelector(
                        title: 'Исполнители',
                        selectedIds: _selectedAssigneeIds,
                        onChanged:
                            (ids) => setState(
                              () => _selectedAssigneeIds = ids.take(1).toList(),
                            ),
                      ),
                      const SizedBox(height: 16),

                      // Соисполнители
                      EmployeeSelector(
                        title: 'Соисполнители',
                        selectedIds: _selectedCoAssigneeIds,
                        onChanged:
                            (ids) =>
                                setState(() => _selectedCoAssigneeIds = ids),
                      ),
                      const SizedBox(height: 24),

                      // Наблюдатели
                      EmployeeSelector(
                        title: 'Наблюдатели',
                        selectedIds: _selectedObserverIds,
                        onChanged:
                            (ids) => setState(() => _selectedObserverIds = ids),
                      ),
                      const SizedBox(height: 16),

                      // Постановщики
                      EmployeeSelector(
                        title: 'Постановщики',
                        selectedIds: _selectedCreatorIds,
                        onChanged:
                            (ids) => setState(() => _selectedCreatorIds = ids),
                      ),
                      const SizedBox(height: 24),

                      // Приоритет и дата
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _priority,
                              decoration: const InputDecoration(
                                labelText: 'Приоритет',
                                border: OutlineInputBorder(),
                              ),
                              items: const [
                                DropdownMenuItem(
                                  value: 'low',
                                  child: Text('Низкий'),
                                ),
                                DropdownMenuItem(
                                  value: 'medium',
                                  child: Text('Средний'),
                                ),
                                DropdownMenuItem(
                                  value: 'high',
                                  child: Text('Высокий'),
                                ),
                                DropdownMenuItem(
                                  value: 'critical',
                                  child: Text('Критический'),
                                ),
                              ],
                              onChanged:
                                  (value) => setState(() => _priority = value!),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: InkWell(
                              onTap: _selectDueDate,
                              child: InputDecorator(
                                decoration: InputDecoration(
                                  labelText: 'Срок выполнения',
                                  border: const OutlineInputBorder(),
                                  suffixIcon:
                                      _dueDate != null
                                          ? IconButton(
                                            icon: const Icon(
                                              Icons.clear,
                                              size: 16,
                                            ),
                                            onPressed:
                                                () => setState(
                                                  () => _dueDate = null,
                                                ),
                                            tooltip: 'Сделать бессрочной',
                                          )
                                          : const Icon(
                                            Icons.calendar_today,
                                            size: 16,
                                          ),
                                ),
                                child: Text(
                                  _dueDate == null
                                      ? 'Не указан (бессрочная)'
                                      : '${_dueDate!.day}.${_dueDate!.month}.${_dueDate!.year}',
                                  style: TextStyle(
                                    color:
                                        _dueDate == null ? Colors.grey : null,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Дополнительные параметры
                      AdditionalOptionsSection(
                        allowAssigneeToChangeDueDate:
                            _allowAssigneeToChangeDueDate,
                        onAllowAssigneeChanged:
                            (value) => setState(
                              () => _allowAssigneeToChangeDueDate = value,
                            ),
                      ),
                      const SizedBox(height: 24),

                      // Файловые вложения
                      UnifiedAttachmentsSection(
                        cloudFiles: _cloudFiles,
                        onAddCloudFile: _addCloudFile,
                        onRemoveCloudFile: _removeCloudFile,
                        onUpdateCloudFile: _updateCloudFile,
                        onTaskAutoSave: _autoSaveTask,
                      ),
                      const SizedBox(height: 24),

                      // Чек-лист
                      ChecklistSection(
                        checklist: _checklist,
                        onAddItem: _addChecklistItem,
                        onRemoveItem:
                            (item) => setState(() => _checklist.remove(item)),
                        onToggleItem:
                            (item, completed) =>
                                setState(() => item.completed = completed),
                        onReorder: _reorderChecklist,
                      ),
                      const SizedBox(height: 24),

                      // Напоминания
                      RemindersSection(
                        reminders: _reminders,
                        onAddReminder: _addReminder,
                        onRemoveReminder:
                            (reminder) =>
                                setState(() => _reminders.remove(reminder)),
                      ),
                      const SizedBox(height: 24),

                      // Повторение
                      RecurrenceSection(
                        recurrenceType: _recurrenceType,
                        recurrenceInterval: _recurrenceInterval,
                        onRecurrenceTypeChanged:
                            (value) => setState(() => _recurrenceType = value),
                        onRecurrenceIntervalChanged:
                            (value) =>
                                setState(() => _recurrenceInterval = value),
                      ),
                      const SizedBox(height: 24),

                      // Перенос праздников
                      HolidayTransferSection(
                        holidayTransferRule: _holidayTransferRule,
                        onHolidayTransferRuleChanged:
                            (value) =>
                                setState(() => _holidayTransferRule = value),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Кнопки
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _hasActiveFileOperations ? null : widget.onCancel,
                  child: const Text('Отмена'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _hasActiveFileOperations ? null : _createTask,
                  child:
                      _hasActiveFileOperations
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : Text(
                            widget.initialTask == null
                                ? 'Создать'
                                : 'Сохранить',
                          ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _selectDueDate() async {
    final date = await SmartDatePickerDialog.show(
      context: context,
      initialDate: _dueDate,
      firstDate: DateTime(2020, 1, 1), // Разрешаем выбор дат в прошлом
      lastDate: DateTime(2030, 12, 31), // Расширенный диапазон будущих дат
      helpText: 'Выберите крайний срок',
      allowClear: true,
    );
    if (date != null) {
      setState(() => _dueDate = date);
    }
  }

  void _addCloudFile(FileAttachmentItem file) {
    setState(() {
      _cloudFiles.add(file);
    });
  }

  void _removeCloudFile(FileAttachmentItem file) {
    setState(() {
      _cloudFiles.remove(file);
    });
  }

  void _updateCloudFile(FileAttachmentItem updatedFile) {
    setState(() {
      final index = _cloudFiles.indexWhere((f) => f.name == updatedFile.name);
      if (index != -1) {
        _cloudFiles[index] = updatedFile;
      }
    });
  }

  void _addChecklistItem() {
    showDialog(
      context: context,
      builder:
          (context) => ChecklistItemDialog(
            onAdd: (item) {
              setState(() => _checklist.add(item));
              Navigator.of(context).pop();
            },
          ),
    );
  }

  void _reorderChecklist(int oldIndex, int newIndex) {
    setState(() {
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final ChecklistItem item = _checklist.removeAt(oldIndex);
      _checklist.insert(newIndex, item);
    });
  }

  void _addReminder() {
    showDialog(
      context: context,
      builder:
          (context) => ReminderDialog(
            onAdd: (reminder) {
              setState(() => _reminders.add(reminder));
              Navigator.of(context).pop();
            },
          ),
    );
  }

  void _createTask() {
    if (_formKey.currentState!.validate()) {
      final task = _buildTaskEntity();

      // Используем приоритетно новые callback'и
      if (widget.onCreated != null) {
        widget.onCreated!(task);
      } else if (widget.onSaved != null) {
        widget.onSaved!(task);
      } else {
        final activeFirmCubit = context.read<ActiveFirmCubit>();
        final activeFirm = activeFirmCubit.state.selectedFirm;
        if (activeFirm != null) {
          context.read<TasksCubit>().saveTask(activeFirm.id, task);
        }
      }
    }
  }

  /// Автосохранение задачи в фоне (например, при загрузке/удалении файлов)
  void _autoSaveTask() {
    // Проверяем, что есть минимально необходимые данные
    if (_titleController.text.trim().isEmpty) {
      return; // Не сохраняем задачу без названия
    }

    final activeFirmCubit = context.read<ActiveFirmCubit>();
    final activeFirm = activeFirmCubit.state.selectedFirm;
    if (activeFirm == null) return;

    final task = _buildTaskEntity();

    // Для существующих задач (редактирование) - просто обновляем
    if (widget.initialTask?.id != null && widget.initialTask!.id.isNotEmpty) {
      // Фоновое сохранение без уведомлений
      context.read<TasksCubit>().saveTask(activeFirm.id, task);
    }
    // Для новых задач - создаем только если есть файлы и еще не создавали автоматически
    else if (!_wasAutoCreated &&
        _cloudFiles.any((f) => f.status == FileAttachmentStatus.uploaded)) {
      // Автоматически создаем задачу при загрузке файлов, чтобы не потерять их
      // Пользователь сможет доредактировать задачу позже
      context.read<TasksCubit>().saveTask(activeFirm.id, task);

      // Помечаем, что задача была автоматически создана
      setState(() {
        _wasAutoCreated = true;
      });

      // Показываем пользователю что задача была автоматически сохранена
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Задача автоматически сохранена при загрузке файла',
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
    // Если задача уже была автоматически создана, продолжаем ее обновлять
    else if (_wasAutoCreated) {
      context.read<TasksCubit>().saveTask(activeFirm.id, task);
    }
  }

  /// Создает объект TaskEntity из текущего состояния формы
  TaskEntity _buildTaskEntity() {
    return TaskEntity(
      id: widget.initialTask?.id ?? '',
      title: _titleController.text,
      description: _descriptionController.text,
      clientIds: _selectedClientIds,
      assigneeIds: [
        ..._selectedAssigneeIds,
        ..._selectedCoAssigneeIds.where(
          (id) => !_selectedAssigneeIds.contains(id),
        ),
      ],
      observerIds: _selectedObserverIds,
      creatorIds: _selectedCreatorIds,
      status: widget.initialTask?.status ?? 'in_progress',
      priority: _priority,
      dueDate: _dueDate,
      attachments: [
        // Облачные файлы с ключами (только успешно загруженные)
        ..._cloudFiles
            .where(
              (f) =>
                  f.status == FileAttachmentStatus.uploaded &&
                  f.fileKey != null,
            )
            .map(
              (f) => {
                'name': f.name,
                'fileKey': f.fileKey!,
                'fileSize': f.fileSize,
              },
            ),
      ],
      checklist:
          _checklist
              .map((c) => {'text': c.text, 'completed': c.completed})
              .toList(),
      reminders:
          _reminders
              .map(
                (r) => {
                  'datetime': r.dateTime.toIso8601String(),
                  'role': r.role,
                },
              )
              .toList(),
      recurrence:
          _recurrenceType == 'none'
              ? null
              : {
                'type': _recurrenceType,
                'interval':
                    _recurrenceType == 'periodic' ? _recurrenceInterval : null,
              },
      options: {
        'allow_assignee_to_change_due_date': _allowAssigneeToChangeDueDate,
        'holiday_transfer_rule': _holidayTransferRule,
      },
      createdAt: widget.initialTask?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
