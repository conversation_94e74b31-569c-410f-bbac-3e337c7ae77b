Идентификатор - d4e4mpjj10hka0o8ct9d
Описание - 📝 Управляет задачами. Позволяет получать списки бессрочных (постранично) и срочных (за месяц) задач, с возможностью фильтрации по клиенту.

Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя.
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы.
		- `action` (string, **обязательно**): `GET`, `UPSERT`, `DELETE`.
		- `task_id` (string, необязательно): ID задачи для `GET` (одна), `UPSERT` (обновление), `DELETE`.
		- `payload` (object, необязательно): Данные для `UPSERT`.
		- **Параметры для `action: GET`**:
			- **Для бессрочных задач:**
				- `page` (integer, необязательно): Номер страницы (с 0). По умолчанию `0`.
			- **Для задач с крайним сроком:**
				- `get_dated_tasks` (boolean, **обязательно**): Установить в `true`.
				- `month` (integer, **обязательно**): Месяц (1-12).
				- `year` (integer, **обязательно**): Год.
			- **Дополнительный фильтр (для любого списка):**
				- `client_id` (string, необязательно): ID клиента для фильтрации списка задач.

Внутренняя работа:
	1. **Авторизация и парсинг**: Как и ранее. Извлекаются все возможные параметры, включая опциональный `client_id`.
	2. **Проверка прав**: Как и ранее.
	3. **Маршрутизация**: В зависимости от `action` вызывается соответствующая функция.
	4. **Выполнение операции `GET`**:
		- **Если передан `task_id`**: Возвращается одна конкретная задача. Фильтр по `client_id` игнорируется.
		- **Если передан `get_dated_tasks: true`**: Выполняется поиск срочных задач за указанный месяц. Если дополнительно передан `client_id`, выборка будет ограничена только задачами этого клиента.
		- **Во всех остальных случаях**: Выполняется постраничный поиск бессрочных задач. Если дополнительно передан `client_id`, пагинация будет применяться только к задачам этого клиента.

На выходе:
	-> `200 OK` (GET, одна задача): `{"data": {...}}`
	-> `200 OK` (GET, бессрочные задачи): `{"metadata": {"total_tasks": ..., "current_page": ...}, "data": [{...}]}`
	-> `200 OK` (GET, срочные задачи): `{"data": [{...}]}`
	-> `201 Created` (UPSERT/create): `{"message": "Task created", "task_id": "..."}`
	-> `200 OK` (UPSERT/update): `{"message": "Task updated", "task_id": "..."}`
	-> `200 OK` (DELETE): `{"message": "Task deleted"}`

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `YDB_ENDPOINT_TASKS`, `YDB_DATABASE_TASKS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`
- **Рекомендация по производительности**: Для ускорения выборки срочных задач рекомендуется добавить в YDB для таблиц `tasks_{firm_id}` вторичный индекс по полю `due_date`.

---
#### index.py
```python
import json, os, logging
import ydb
from utils import auth_utils, ydb_utils, request_parser

from get import get_task
from upsert import upsert_task
from delete import delete_task
from custom_errors import AuthError, LogicError, NotFoundError

logging.basicConfig(level=logging.INFO)

def check_membership_and_role(session, user_id, firm_id):
    query_text = "DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;"
    query = session.prepare(query_text)
    result = session.transaction(ydb.SerializableReadWrite()).execute(query, {"$user_id": user_id, "$firm_id": firm_id}, commit_tx=True)
    if not result[0].rows:
        return (False, False)
    roles = json.loads(result[0].rows[0].roles)
    is_admin_or_owner = "OWNER" in roles or "ADMIN" in roles
    return (True, is_admin_or_owner)

def handler(event, context):
    try:
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '): raise AuthError("Unauthorized")
        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload: raise AuthError("Invalid token")
        requesting_user_id = user_payload['user_id']

        data = request_parser.parse_request_body(event)
        firm_id = data.get('firm_id')
        action = data.get('action')
        
        if not all([firm_id, action]): raise LogicError("firm_id and action are required.")

        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)
        is_member, is_admin_or_owner = firms_pool.retry_operation_sync(
            lambda s: check_membership_and_role(s, requesting_user_id, firm_id)
        )

        if not is_member: raise AuthError("User is not a member of the specified firm.")

        tasks_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_TASKS"], os.environ["YDB_DATABASE_TASKS"])
        tasks_pool = ydb.SessionPool(tasks_driver)
        table_name = f"tasks_{firm_id}"

        def task_transaction_router(session):
            task_id = data.get('task_id')
            payload = data.get('payload', {})
            page = data.get('page')
            get_dated_tasks = data.get('get_dated_tasks', False)
            month = data.get('month')
            year = data.get('year')
            client_id = data.get('client_id') # Новый параметр

            if action == "GET":
                # Передаем client_id в функцию get_task
                return get_task(session, table_name, task_id, page, get_dated_tasks, month, year, client_id)
            
            elif action == "UPSERT":
                return upsert_task(session, table_name, payload, task_id, requesting_user_id, is_admin_or_owner)
            
            elif action == "DELETE":
                return delete_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner)
            
            else:
                raise LogicError(f"Invalid action.")

        return tasks_pool.retry_operation_sync(task_transaction_router)

    except AuthError as e: return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e: return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e: return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error processing task request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```
#### get.py
```python
import json
import ydb
import math
import datetime
import calendar
from custom_errors import NotFoundError, LogicError

PAGE_SIZE = 100

def get_task(session, table_name, task_id=None, page=0, get_dated_tasks=False, month=None, year=None, client_id=None):
    """
    Обрабатывает GET запросы с опциональной фильтрацией по client_id.
    """
    tx = session.transaction(ydb.SerializableReadWrite())
    
    if task_id:
        query_text = f"DECLARE $task_id AS Utf8; SELECT * FROM `{table_name}` WHERE task_id = $task_id;"
        res = tx.execute(session.prepare(query_text), {"$task_id": task_id})
        if not res[0].rows:
            raise NotFoundError(f"Task with id {task_id} not found.")
        data = {c.name: res[0].rows[0][c.name] for c in res[0].columns}
        tx.commit()
        return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}

    params = {}
    declare_clauses = []
    where_clauses = []
    
    if client_id:
        declare_clauses.append("DECLARE $client_id_str AS Utf8;")
        # ИСПРАВЛЕНО: Используем правильную функцию YQL String::Contains для поиска в JSON-строке.
        where_clauses.append("String::Contains(CAST(client_ids_json AS String), $client_id_str)")
        # Ищем ID в кавычках, чтобы избежать частичных совпадений (например, "123" в "1234").
        params["$client_id_str"] = f'"{client_id}"'
    
    if get_dated_tasks:
        if not all([month, year]):
            raise LogicError("`month` and `year` are required when `get_dated_tasks` is true.")
        
        try:
            _, num_days = calendar.monthrange(year, month)
            start_date = datetime.datetime(year, month, 1, tzinfo=datetime.timezone.utc)
            end_date = datetime.datetime(year, month, num_days, 23, 59, 59, 999999, tzinfo=datetime.timezone.utc)
        except (ValueError, TypeError):
            raise LogicError("Invalid month or year provided.")
        
        declare_clauses.extend(["DECLARE $start_date AS Timestamp;", "DECLARE $end_date AS Timestamp;"])
        where_clauses.extend(["due_date IS NOT NULL", "due_date >= $start_date", "due_date <= $end_date"])
        params.update({"$start_date": start_date, "$end_date": end_date})
        
        full_where_clause = " AND ".join(where_clauses)
        full_declare_clause = " ".join(declare_clauses)
        
        query_text = f"{full_declare_clause} SELECT * FROM `{table_name}` WHERE {full_where_clause} ORDER BY due_date ASC;"
        res = tx.execute(session.prepare(query_text), params)
        data = [{c.name: r[c.name] for c in res[0].columns} for r in res[0].rows]
        tx.commit()
        return {"statusCode": 200, "body": json.dumps({"data": data}, default=str)}

    where_clauses.append("due_date IS NULL")
    full_where_clause = " AND ".join(where_clauses)
    
    try:
        page = int(page or 0)
        if page < 0: page = 0
    except (ValueError, TypeError):
        page = 0
        
    count_declare_clause = " ".join(declare_clauses)
    count_query_text = f"{count_declare_clause} SELECT COUNT(task_id) AS total_count FROM `{table_name}` WHERE {full_where_clause};"
    count_res = tx.execute(session.prepare(count_query_text), params)
    total_tasks = count_res[0].rows[0].total_count if count_res[0].rows else 0
    
    if total_tasks == 0:
        metadata = {"total_tasks": 0, "current_page": 0, "page_size": PAGE_SIZE, "total_pages": 0}
        tx.commit()
        return {"statusCode": 200, "body": json.dumps({"metadata": metadata, "data": []})}

    total_pages = math.ceil(total_tasks / PAGE_SIZE)
    if page >= total_pages:
        raise NotFoundError(f"Page {page} does not exist. Total pages: {total_pages}.")

    offset = page * PAGE_SIZE
    
    declare_clauses.extend(["DECLARE $limit AS Uint64;", "DECLARE $offset AS Uint64;"])
    params.update({"$limit": PAGE_SIZE, "$offset": offset})
    
    full_declare_clause = " ".join(declare_clauses)
    
    select_query_text = f"""
        {full_declare_clause}
        SELECT * FROM `{table_name}`
        WHERE {full_where_clause}
        ORDER BY created_at DESC
        LIMIT $limit OFFSET $offset;
    """
    select_res = tx.execute(session.prepare(select_query_text), params)
    data = [{c.name: r[c.name] for c in select_res[0].columns} for r in select_res[0].rows]
    
    metadata = {"total_tasks": total_tasks, "current_page": page, "page_size": PAGE_SIZE, "total_pages": total_pages}
    
    tx.commit()
    return {"statusCode": 200, "body": json.dumps({"metadata": metadata, "data": data}, default=str)}
```
#### upsert.py
```python
# ФАЙЛ БЕЗ ИЗМЕНЕНИЙ
import json, uuid, datetime, pytz
import ydb
from custom_errors import LogicError, AuthError, NotFoundError

def _get_declare_for_task(payload):
    declare_clauses, params = "", {}
    type_map = {'task_id': ydb.PrimitiveType.Utf8, 'title': ydb.OptionalType(ydb.PrimitiveType.Utf8), 'description': ydb.OptionalType(ydb.PrimitiveType.Utf8), 'client_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 'assignee_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 'observer_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 'creator_ids_json': ydb.OptionalType(ydb.PrimitiveType.Json), 'status': ydb.OptionalType(ydb.PrimitiveType.Utf8), 'priority': ydb.OptionalType(ydb.PrimitiveType.Utf8), 'due_date': ydb.OptionalType(ydb.PrimitiveType.Timestamp), 'completed_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp), 'attachments_json': ydb.OptionalType(ydb.PrimitiveType.Json), 'checklist_json': ydb.OptionalType(ydb.PrimitiveType.Json), 'reminders_json': ydb.OptionalType(ydb.PrimitiveType.Json), 'recurrence_json': ydb.OptionalType(ydb.PrimitiveType.Json), 'options_json': ydb.OptionalType(ydb.PrimitiveType.Json), 'holiday_transfer_rule': ydb.OptionalType(ydb.PrimitiveType.Utf8), 'origin_task_id': ydb.OptionalType(ydb.PrimitiveType.Utf8), 'created_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp), 'updated_at': ydb.OptionalType(ydb.PrimitiveType.Timestamp)}
    for key, value in payload.items():
        if key in type_map:
            if isinstance(type_map[key], ydb.OptionalType) and type_map[key].item == ydb.PrimitiveType.Timestamp and isinstance(value, str):
                try: params[f"${key}"] = datetime.datetime.strptime(value.replace('Z', ''), "%Y-%m-%dT%H:%M:%S").replace(tzinfo=pytz.utc)
                except (ValueError, TypeError): raise LogicError(f"Invalid timestamp format for '{key}'. Use YYYY-MM-DDTHH:MM:SSZ.")
            else: params[f"${key}"] = value
            type_name_str = str(type_map[key]); type_name = type_name_str.replace("Optional[", "").replace("]", "") if "Optional" in type_name_str else type_name_str; declare_clauses += f"DECLARE ${key} AS {type_name}; "
    return declare_clauses, params

def _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner):
    if is_admin_or_owner: return True
    res = session.transaction(ydb.SerializableReadWrite()).execute(session.prepare(f"DECLARE $task_id AS Utf8; SELECT creator_ids_json, assignee_ids_json, options_json FROM `{table_name}` WHERE task_id = $task_id;"), {"$task_id": task_id})
    if not res[0].rows: raise NotFoundError(f"Task with id {task_id} not found.")
    task_data = res[0].rows[0]
    if requesting_user_id in json.loads(task_data.get('creator_ids_json', '[]') or '[]'): return True
    if requesting_user_id in json.loads(task_data.get('assignee_ids_json', '[]') or '[]'):
        if json.loads(task_data.get('options_json', '{}') or '{}').get('allow_assignee_to_edit'): return True
    return False

def upsert_task(session, table_name, payload, task_id, requesting_user_id, is_admin_or_owner):
    if not payload: raise LogicError("payload is required for UPSERT action.")
    tx = session.transaction(ydb.SerializableReadWrite())
    payload['updated_at'] = datetime.datetime.now(pytz.utc)
    if task_id:
        if not _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner): raise AuthError("You do not have permission to edit this task.")
        payload['task_id'] = task_id
        declare_clauses, params = _get_declare_for_task(payload)
        set_clauses = ", ".join([f"`{k}` = ${k}" for k in payload if k != 'task_id'])
        tx.execute(session.prepare(f"{declare_clauses} UPDATE `{table_name}` SET {set_clauses} WHERE task_id = $task_id;"), params)
        tx.commit()
        return {"statusCode": 200, "body": json.dumps({"message": "Task updated", "task_id": task_id})}
    else:
        payload['task_id'] = str(uuid.uuid4()); payload['created_at'] = payload['updated_at']
        if 'creator_ids_json' not in payload: payload['creator_ids_json'] = json.dumps([requesting_user_id])
        options = json.loads(payload.get('options_json', '{}') or '{}')
        if 'allow_assignee_to_edit' not in options: options['allow_assignee_to_edit'] = True
        payload['options_json'] = json.dumps(options)
        declare_clauses, params = _get_declare_for_task(payload)
        columns = ", ".join([f"`{k}`" for k in payload.keys()]); placeholders = ", ".join([f"${k}" for k in payload.keys()])
        tx.execute(session.prepare(f"{declare_clauses} UPSERT INTO `{table_name}` ({columns}) VALUES ({placeholders});"), params)
        tx.commit()
        return {"statusCode": 201, "body": json.dumps({"message": "Task created", "task_id": payload['task_id']})}
```
#### delete.py
```python
# ФАЙЛ БЕЗ ИЗМЕНЕНИЙ
import json
import ydb
from custom_errors import LogicError, AuthError, NotFoundError

def _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner):
    if is_admin_or_owner: return True
    res = session.transaction(ydb.SerializableReadWrite()).execute(session.prepare(f"DECLARE $task_id AS Utf8; SELECT creator_ids_json, assignee_ids_json, options_json FROM `{table_name}` WHERE task_id = $task_id;"), {"$task_id": task_id})
    if not res[0].rows: raise NotFoundError(f"Task with id {task_id} not found.")
    task_data = res[0].rows[0]
    if requesting_user_id in json.loads(task_data.get('creator_ids_json', '[]') or '[]'): return True
    if requesting_user_id in json.loads(task_data.get('assignee_ids_json', '[]') or '[]'):
        if json.loads(task_data.get('options_json', '{}') or '{}').get('allow_assignee_to_edit'): return True
    return False

def delete_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner):
    if not task_id: raise LogicError("task_id is required for DELETE action.")
    if not _can_user_modify_task(session, table_name, task_id, requesting_user_id, is_admin_or_owner): raise AuthError("You do not have permission to delete this task.")
    tx = session.transaction(ydb.SerializableReadWrite())
    tx.execute(session.prepare(f"DECLARE $task_id AS Utf8; DELETE FROM `{table_name}` WHERE task_id = $task_id;"), {"$task_id": task_id})
    tx.commit()
    return {"statusCode": 200, "body": json.dumps({"message": "Task deleted"})}
```
#### custom_errors.py
```python
# ФАЙЛ БЕЗ ИЗМЕНЕНИЙ
class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass
```

---
#### Запросы для тестирования
```json
// СЦЕНАРИЙ 1: Получение бессрочных задач (без фильтра по клиенту)
{
  "httpMethod": "POST",
  "headers": { "Authorization": "Bearer <YOUR_JWT_TOKEN>" },
  "body": "{\"firm_id\": \"...\", \"action\": \"GET\", \"page\": 0}"
}
```

```json
// СЦЕНАРИЙ 2: Получение бессрочных задач ТОЛЬКО для клиента "client-123"
{
  "httpMethod": "POST",
  "headers": { "Authorization": "Bearer <YOUR_JWT_TOKEN>" },
  "body": "{\"firm_id\": \"...\", \"action\": \"GET\", \"page\": 0, \"client_id\": \"client-123\"}"
}
```

```json
// СЦЕНАРИЙ 3: Получение срочных задач за Июль 2025 (без фильтра по клиенту)
{
  "httpMethod": "POST",
  "headers": { "Authorization": "Bearer <YOUR_JWT_TOKEN>" },
  "body": "{\"firm_id\": \"...\", \"action\": \"GET\", \"get_dated_tasks\": true, \"month\": 7, \"year\": 2025}"
}
```

```json
// СЦЕНАРИЙ 4: Получение срочных задач за Июль 2025 ТОЛЬКО для клиента "client-123"
{
  "httpMethod": "POST",
  "headers": { "Authorization": "Bearer <YOUR_JWT_TOKEN>" },
  "body": "{\"firm_id\": \"...\", \"action\": \"GET\", \"get_dated_tasks\": true, \"month\": 7, \"year\": 2025, \"client_id\": \"client-123\"}"
}
```

```json
// СЦЕНАРИЙ 5: Создание задачи, привязанной к нескольким клиентам
{
  "httpMethod": "POST",
  "headers": { "Authorization": "Bearer <YOUR_JWT_TOKEN>" },
  "body": "{\"firm_id\": \"...\", \"action\": \"UPSERT\", \"payload\": {\"title\": \"Общая задача для двух клиентов\", \"client_ids_json\": \"[\\\"client-123\\\", \\\"client-456\\\"]\"}}"
}
```