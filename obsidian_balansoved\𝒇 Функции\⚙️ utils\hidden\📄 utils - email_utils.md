**1. `send_verification_code(email: str, code: str) -> bool`**
- **На входе**:
	-> `email`: Адрес электронной почты получателя.
	-> `code`: Строка с 6-значным кодом для отправки.
- **Внутренняя работа**:
	1. Проверяет наличие переменных окружения `EMAIL_API_URL` и `EMAIL_API_KEY`.
	2. Если переменные не заданы, логирует сообщение о "мок-отправке" и возвращает `True`, чтобы не блокировать процесс разработки.
	3. Если переменные заданы, формирует заголовки и тело HTTP-запроса для внешнего API отправки почты.
	4. Отправляет POST-запрос с помощью библиотеки `requests`.
	5. Проверяет статус-код ответа от API.
- **На выходе**:
	-> `True`, если API вернуло успешный статус (200 OK) или если активен режим "мок-отправки".
	-> `False`, если произошла ошибка при отправке запроса или API вернуло ошибку.

---

```python
# email_utils.py

import requests
import os
import logging

# Константы для API UniSender
UNISENDER_API_URL = "https://api.unione.io/ru/transactional/api/v1/email/send.json"
UNISENDER_API_KEY = os.environ.get("UNISENDER_API_KEY")
SENDER_EMAIL = os.environ.get("SENDER_EMAIL", "<EMAIL>")
SENDER_NAME = "Auth Service" # Имя отправителя, которое увидит пользователь

def send_verification_code(email: str, code: str) -> bool:
    """
    Отправляет 6-значный код на указанный email с помощью UniSender.
    """
    if not UNISENDER_API_KEY:
        logging.error("Ключ API для UniSender (UNISENDER_API_KEY) не установлен в переменных окружения.")
        # Для отладки можно логировать, но не блокировать процесс
        logging.info(f"МОК-ОТПРАВКА (нет ключа): Код {code} для {email}")
        return True # В продакшене лучше возвращать False

    # Формируем тело запроса строго по документации UniSender
    payload = {
        "api_key": UNISENDER_API_KEY,
        "message": {
            "recipients": [
                {
                    "email": email
                }
            ],
            "body": {
                "plaintext": f"Ваш код подтверждения: {code}",
                "html": f"<h3>Ваш код подтверждения</h3><p>Ваш код: <strong>{code}</strong></p>"
            },
            "subject": "Код подтверждения",
            "from_email": SENDER_EMAIL,
            "from_name": SENDER_NAME
        }
    }

    try:
        response = requests.post(UNISENDER_API_URL, json=payload, timeout=10)

        # Проверяем не только статус-код, но и ответ от самого UniSender
        if response.status_code == 200:
            response_data = response.json()
            if response_data.get("status") == "success":
                logging.info(f"Письмо с кодом успешно отправлено на {email} через UniSender.")
                return True
            else:
                # Ошибка на стороне UniSender (например, неверный email)
                logging.error(f"Ошибка от UniSender при отправке на {email}: {response_data.get('message')}")
                return False
        else:
            # Ошибка на уровне HTTP
            logging.error(f"HTTP ошибка при обращении к UniSender: {response.status_code} {response.text}")
            return False

    except requests.RequestException as e:
        logging.error(f"Сетевая ошибка при отправке запроса в UniSender: {e}")
        return False
```