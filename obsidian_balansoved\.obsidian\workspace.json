{"main": {"id": "d3536f1e300273df", "type": "split", "children": [{"id": "a1e337f219753ed1", "type": "tabs", "children": [{"id": "86b3c56d65f0c23e", "type": "leaf", "state": {"type": "markdown", "state": {"file": "𝒇 Функции/✳️💰 tariffs-and-storage-manager - CloudFunction функция.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "✳️💰 tariffs-and-storage-manager - CloudFunction функция"}}, {"id": "facc57ffea97a39a", "type": "leaf", "state": {"type": "markdown", "state": {"file": "🗄️ Базы данных/💾 tariffs-and-storage-database - База данных YandexDatabase.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "💾 tariffs-and-storage-database - База данных YandexDatabase"}}, {"id": "b993be8cd31f51ff", "type": "leaf", "state": {"type": "markdown", "state": {"file": "🔗 gateway-api/🔥 tariffs-and-storage-api - Gateway-api Шлюз.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "🔥 tariffs-and-storage-api - Gateway-api Шлюз"}}, {"id": "6ec8ea571d4caa21", "type": "leaf", "state": {"type": "markdown", "state": {"file": "𝒇 Функции/⚙️ utils/📃 utils- storage_utils.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "📃 utils- storage_utils"}}, {"id": "3ff095fbd5e2c1de", "type": "leaf", "state": {"type": "markdown", "state": {"file": "📜 json/ydb_sa_key.json.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "ydb_sa_key.json"}}], "currentTab": 3}], "direction": "vertical"}, "left": {"id": "6d8c5fb9e49f0b67", "type": "split", "children": [{"id": "c198cdd655ff79f5", "type": "tabs", "children": [{"id": "48b2ad103adbf23b", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Файловый менеджер"}}, {"id": "71fc7d877d2d03c7", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Поиск"}}, {"id": "5c463bff54b2d9e2", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Закладки"}}]}], "direction": "horizontal", "width": 300.5}, "right": {"id": "d430a80be19e1aec", "type": "split", "children": [{"id": "f7f073c62502c985", "type": "tabs", "children": [{"id": "820c830b01c6ec5b", "type": "leaf", "state": {"type": "backlink", "state": {"file": "𝒇 Функции/✳️📝 edit-task - CloudFunction функция.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Обратные ссылки для ✳️📝 edit-task - CloudFunction функция"}}, {"id": "503783fd2188cffd", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "𝒇 Функции/✳️📝 edit-task - CloudFunction функция.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Исходящие ссылки из ✳️📝 edit-task - CloudFunction функция"}}, {"id": "0861aa2d16e98034", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Теги"}}, {"id": "70d3152b4dc539c2", "type": "leaf", "state": {"type": "outline", "state": {"file": "𝒇 Функции/✳️📝 edit-task - CloudFunction функция.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Структура ✳️📝 edit-task - CloudFunction функция"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Меню быстрого перехода": false, "graph:Граф": false, "canvas:Создать новый холст": false, "daily-notes:Сегодняшняя заметка": false, "templates:Вставить шаблон": false, "command-palette:Открыть палитру команд": false}}, "active": "6ec8ea571d4caa21", "lastOpenFiles": ["𝒇 Функции/✳️💰 tariffs-and-storage-manager - CloudFunction функция.md", "𝒇 Функции/⚙️ utils/📃 utils- storage_utils.md", "🔗 gateway-api/🔥 tariffs-and-storage-api - Gateway-api Шлюз.md", "𝒇 Функции/⚙️ utils/📄 utils - request_parser.md", "𝒇 Функции/⚙️ utils/📄 utils - ydb_utils.md", "🔗 gateway-api/🔥 tasks-api - Gateway-api Шлюз.md", "𝒇 Функции/💉 requirements.txt (общий).md", "🗝️ Ключи доступа/🗝️ auth-service-acc - Статический ключ доступа.md", "🗝️ Ключи доступа", "📜 json/ydb_sa_key.json.md", "🗄️ Базы данных/💾 tariffs-and-storage-database - База данных YandexDatabase.md", "🗄️ Базы данных/💾 firms-database - База данных YandexDatabase.md", "𝒇 Функции/⚙️ utils/📄 utils - auth_utils.md", "𝒇 Функции/✳️🗄️ storage-manager - CloudFunction функция.md", "𝒇 Функции/✳️📝 edit-task - CloudFunction функция.md", "𝒇 Функции/✳️👤 edit-client - CloudFunction функция.md", "🗄️ Базы данных/💾 tasks-database - База данных YandexDatabase.md", "🗄️ Базы данных/💾 jwt-database - База данных YandexDatabase.md", "📨 Структура MessageQueue.md", "🗃️ Структура YDB.md", "🔗 gateway-api/hidden/🛡️ auth-gate-checker - Gateway-api Шлюз.md", "🔗 gateway-api/hidden", "🔗 gateway-api/Новая папка", "𝒇 Функции/⚙️ utils/hidden/📄 utils - email_utils.md", "𝒇 Функции/⚙️ utils/hidden", "𝒇 Функции/⚙️ utils/Новая папка", "🔗 gateway-api/🔥 storage-api - Gateway-api Шлюз.md", "🔗 gateway-api/🔥 employees-and-firms-api - Gateway-api Шлюз.md", "𝒇 Функции/✳️📎 delete-employee - CloudFunction функция.md", "🔗 gateway-api/🔥 clients-api - Gateway-api Шлюз.md", "🔗 gateway-api/🔥 auth-api - Gateway-api Шлюз.md", "🗄️ Базы данных", "𝒇 Функции/⚙️ utils/🐍 test/email_utils.py", "𝒇 Функции/⚙️ utils/🐍 test/Text Document.txt", "𝒇 Функции/⚙️ utils/🐍 test/Архив WinRAR.rar", "𝒇 Функции/⚙️ utils/🐍 test/test_sender.py", "𝒇 Функции/⚙️ utils/🐍 test/Bitmap image.bmp"]}