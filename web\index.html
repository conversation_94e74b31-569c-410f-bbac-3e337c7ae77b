<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="balansoved_enterprise">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicons (разные для светлой/тёмной темы) -->
  <!-- Светлая тема – чёрный логотип -->
  <link rel="icon" type="image/png" href="favicon.png" media="(prefers-color-scheme: light)" />
  <!-- Тёмная тема – белый/светлый логотип (создайте файл favicon_light.png) -->
  <link rel="icon" type="image/png" href="favicon_light.png" media="(prefers-color-scheme: dark)" />

  <title>balansoved_enterprise</title>
  <link rel="manifest" href="manifest.json">

  <script src="assets/packages/flutter_dropzone/assets/flutter_dropzone.js" defer></script>

  <style>
    /* Splash-screen while Flutter загружается */
    #splash {
      position: fixed;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #ffffff;
      z-index: 9999;
    }

    #splash img {
      width: 120px;
      height: auto;
      /* При плавном появлении Flutter-ка можно добавить animation */
    }

    /* Автоматическая адаптация к системной теме */
    @media (prefers-color-scheme: dark) {
      #splash {
        background: #121212;
      }

      #splash img {
        /* Инвертируем цвета: чёрный логотип станет белым */
        filter: invert(1);
      }
    }
  </style>
</head>

<body>
  <script src='https://cdn.jsdelivr.net/npm/pdfjs-dist@4.6.82/build/pdf.min.mjs' type='module'></script>
  <script type='module'>
  var { pdfjsLib } = globalThis;
  pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.6.82/build/pdf.worker.mjs';

  var pdfRenderOptions = {
    cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.6.82/cmaps/',
    cMapPacked: true,
  }
  </script>
  <!-- Splash-screen логотип -->
  <div id="splash">
    <img src="icons/Icon-192.png" alt="Balansoved logo" />
  </div>
  <script src="flutter_bootstrap.js" async></script>

  <!-- File picker support for web -->
  <script>
    // Поддержка file_picker для веб
    window.addEventListener('flutter-first-frame', function () {
      const splash = document.getElementById('splash');
      if (splash) splash.remove();
    });
  </script>
</body>

</html>