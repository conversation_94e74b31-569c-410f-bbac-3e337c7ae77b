import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/entities/employee_entity.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/get_employees_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/create_employee_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/add_role_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/remove_role_usecase.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/usecases/delete_employee_usecase.dart';

part 'employees_state.dart';

class EmployeesCubit extends Cubit<EmployeesState> {
  final GetEmployeesUseCase getEmployeesUseCase;
  final CreateEmployeeUseCase createEmployeeUseCase;
  final AddRoleUseCase addRoleUseCase;
  final RemoveRoleUseCase removeRoleUseCase;
  final DeleteEmployeeUseCase deleteEmployeeUseCase;

  EmployeesCubit({
    required this.getEmployeesUseCase,
    required this.createEmployeeUseCase,
    required this.addRoleUseCase,
    required this.removeRoleUseCase,
    required this.deleteEmployeeUseCase,
  }) : super(const EmployeesState.initial());

  Future<void> fetchEmployees(String firmId) async {
    if (state.isLoading) return; // avoid duplicate
    emit(state.copyWith(isLoading: true, error: null));
    final result = await getEmployeesUseCase(firmId);
    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, error: failure.message)),
      (list) => emit(state.copyWith(isLoading: false, employees: list)),
    );
  }

  Future<void> addEmployee(String firmId, String email) async {
    if (state.isLoading) return;
    emit(state.copyWith(isLoading: true, error: null));
    final result = await createEmployeeUseCase(firmId, email);
    await result.fold(
      (failure) async {
        emit(state.copyWith(isLoading: false, error: failure.message));
      },
      (_) async {
        // Сбрасываем флаг загрузки перед обновлением списка
        emit(state.copyWith(isLoading: false));
        await fetchEmployees(firmId);
      },
    );
  }

  Future<void> toggleAdminRole(
    String firmId,
    String userId,
    bool currentlyAdmin,
  ) async {
    emit(state.copyWith(isLoading: true, error: null));
    final result =
        currentlyAdmin
            ? await removeRoleUseCase(firmId, userId, 'ADMIN')
            : await addRoleUseCase(firmId, userId, 'ADMIN');
    result.fold(
      (failure) {
        emit(state.copyWith(isLoading: false, error: failure.message));
      },
      (_) async {
        emit(state.copyWith(isLoading: false));
        await fetchEmployees(firmId);
      },
    );
  }

  Future<void> deleteEmployee(String firmId, String userId) async {
    emit(state.copyWith(isLoading: true, error: null));
    final result = await deleteEmployeeUseCase(firmId, userId);
    result.fold(
      (failure) {
        emit(state.copyWith(isLoading: false, error: failure.message));
      },
      (_) async {
        emit(state.copyWith(isLoading: false));
        await fetchEmployees(firmId);
      },
    );
  }
}
