import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';

/// Абстракция удалённого источника данных для клиентов.
/// Реализует универсальный endpoint /manage на clients-api.
abstract class IClientsRemoteDataSource {
  /// Получить список клиентов фирмы.
  Future<List<ClientEntity>> fetchClients(String token, String firmId);

  /// Создать/обновить клиента.
  Future<void> upsertClient(String token, String firmId, ClientEntity client);

  /// Удалить клиента по идентификатору.
  Future<void> deleteClient(String token, String firmId, String clientId);
}
