import 'package:balansoved_enterprise/core/error/exception.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/auth/data/data_source/auth_local_data_source.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/data/data_source/employees_remote_data_source.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/entities/employee_entity.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/domain/repositories/employees_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';

class EmployeesRepositoryImpl implements IEmployeesRepository {
  final IEmployeesRemoteDataSource remote;
  final IAuthLocalDataSource localAuth;

  EmployeesRepositoryImpl({required this.remote, required this.localAuth});

  @override
  Future<Either<Failure, List<EmployeeEntity>>> getEmployees(
    String firmId,
  ) async {
    try {
      NetworkLogger.printInfo('EMP REPO: Получаем JWT из локального хранилища');
      final token = await localAuth.getAccessToken();
      if (token == null || token.isEmpty) {
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      final list = await remote.fetchEmployees(token, firmId);
      NetworkLogger.printSuccess(
        'EMP REPO: Данные получены (Entity count: ${list.length})',
      );
      for (final e in list) {
        NetworkLogger.printInfo('   • $e');
      }
      return Right(list);
    } catch (e) {
      NetworkLogger.printError('EMP REPO: Error getting employees:', e);
      return Left(
        NetworkFailure(message: 'Ошибка при получении сотрудников: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> addEmployee(String firmId, String email) async {
    try {
      NetworkLogger.printInfo('EMP REPO: Добавляем сотрудника ($email)');
      final token = await localAuth.getAccessToken();
      if (token == null || token.isEmpty) {
        return Left(ConnectionFailure(message: 'Токен доступа отсутствует'));
      }
      await remote.addEmployee(token, firmId, email);
      NetworkLogger.printSuccess('EMP REPO: Сотрудник добавлен');
      return const Right(unit);
    } catch (e) {
      NetworkLogger.printError('EMP REPO: Error creating employee:', e);
      return Left(
        NetworkFailure(message: 'Ошибка при создании сотрудника: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> addRole(
    String firmId,
    String userId,
    String role,
  ) async {
    return _roleAction(firmId, userId, role, true);
  }

  @override
  Future<Either<Failure, Unit>> removeRole(
    String firmId,
    String userId,
    String role,
  ) async {
    return _roleAction(firmId, userId, role, false);
  }

  Future<Either<Failure, Unit>> _roleAction(
    String firmId,
    String userId,
    String role,
    bool isAdd,
  ) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null || token.isEmpty) {
        return Left(ServerFailure(message: 'Пользователь не авторизован'));
      }
      if (isAdd) {
        await remote.addRole(token, firmId, userId, role);
      } else {
        await remote.removeRole(token, firmId, userId, role);
      }
      return const Right(unit);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, details: e.toString()));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, details: e.toString()));
    } catch (e, st) {
      return Left(
        UnexpectedFailure(message: e.toString(), details: st.toString()),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> deleteEmployee(
    String firmId,
    String userId,
  ) async {
    try {
      final token = await localAuth.getAccessToken();
      if (token == null || token.isEmpty) {
        return Left(ServerFailure(message: 'Пользователь не авторизован'));
      }
      await remote.deleteEmployee(token, firmId, userId);
      return const Right(unit);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, details: e.toString()));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, details: e.toString()));
    } catch (e, st) {
      return Left(
        UnexpectedFailure(message: e.toString(), details: st.toString()),
      );
    }
  }
}
