import 'package:equatable/equatable.dart';

class TaskEntity extends Equatable {
  final String id;
  final String title;
  final String? description;
  final List<String> clientIds;
  final List<String> assigneeIds;
  final List<String> observerIds;
  final List<String> creatorIds;
  final String status;
  final String priority;
  final DateTime? dueDate;
  final DateTime? completedAt;
  final List<Map<String, dynamic>> attachments;
  final List<Map<String, dynamic>> checklist;
  final List<Map<String, dynamic>> reminders;
  final Map<String, dynamic>? recurrence;
  final Map<String, dynamic> options;
  final String? holidayTransferRule;
  final String? originTaskId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TaskEntity({
    required this.id,
    required this.title,
    this.description,
    required this.clientIds,
    required this.assigneeIds,
    required this.observerIds,
    required this.creatorIds,
    required this.status,
    required this.priority,
    this.dueDate,
    this.completedAt,
    required this.attachments,
    required this.checklist,
    required this.reminders,
    this.recurrence,
    required this.options,
    this.holidayTransferRule,
    this.originTaskId,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    clientIds,
    assigneeIds,
    observerIds,
    creatorIds,
    status,
    priority,
    dueDate,
    completedAt,
    attachments,
    checklist,
    reminders,
    recurrence,
    options,
    holidayTransferRule,
    originTaskId,
    createdAt,
    updatedAt,
  ];

  TaskEntity copyWith({
    String? id,
    String? title,
    String? description,
    List<String>? clientIds,
    List<String>? assigneeIds,
    List<String>? observerIds,
    List<String>? creatorIds,
    String? status,
    String? priority,
    DateTime? dueDate,
    DateTime? completedAt,
    List<Map<String, dynamic>>? attachments,
    List<Map<String, dynamic>>? checklist,
    List<Map<String, dynamic>>? reminders,
    Map<String, dynamic>? recurrence,
    Map<String, dynamic>? options,
    String? holidayTransferRule,
    String? originTaskId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TaskEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      clientIds: clientIds ?? this.clientIds,
      assigneeIds: assigneeIds ?? this.assigneeIds,
      observerIds: observerIds ?? this.observerIds,
      creatorIds: creatorIds ?? this.creatorIds,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      dueDate: dueDate ?? this.dueDate,
      completedAt: completedAt ?? this.completedAt,
      attachments: attachments ?? this.attachments,
      checklist: checklist ?? this.checklist,
      reminders: reminders ?? this.reminders,
      recurrence: recurrence ?? this.recurrence,
      options: options ?? this.options,
      holidayTransferRule: holidayTransferRule ?? this.holidayTransferRule,
      originTaskId: originTaskId ?? this.originTaskId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Тип задач для фильтрации
enum TaskViewType {
  timeless, // Бессрочные задачи (due_date IS NULL)
  dated, // Задачи с крайним сроком (due_date IS NOT NULL)
}

/// Параметры запроса задач
class TaskRequestParams extends Equatable {
  final TaskViewType viewType;
  final int? page; // Для бессрочных задач (пагинация)
  final int? month; // Для срочных задач (1-12)
  final int? year; // Для срочных задач
  final String? clientId;

  const TaskRequestParams({
    required this.viewType,
    this.page,
    this.month,
    this.year,
    this.clientId,
  });

  /// Создать параметры для бессрочных задач
  factory TaskRequestParams.timeless({int page = 0, String? clientId}) {
    return TaskRequestParams(
      viewType: TaskViewType.timeless,
      page: page,
      clientId: clientId,
    );
  }

  /// Создать параметры для задач с крайним сроком
  factory TaskRequestParams.dated({
    required int month,
    required int year,
    String? clientId,
  }) {
    return TaskRequestParams(
      viewType: TaskViewType.dated,
      month: month,
      year: year,
      clientId: clientId,
    );
  }

  /// Создать параметры для текущего месяца
  factory TaskRequestParams.currentMonth({String? clientId}) {
    final now = DateTime.now();
    return TaskRequestParams(
      viewType: TaskViewType.dated,
      month: now.month,
      year: now.year,
      clientId: clientId,
    );
  }

  String get displayText {
    switch (viewType) {
      case TaskViewType.timeless:
        return 'Бессрочные задачи${page != null ? ' (стр. ${page! + 1})' : ''}';
      case TaskViewType.dated:
        if (month != null && year != null) {
          return '${_getMonthName(month!)} $year';
        }
        return 'Задачи с крайним сроком';
    }
  }

  String _getMonthName(int month) {
    const months = [
      'Январь',
      'Февраль',
      'Март',
      'Апрель',
      'Май',
      'Июнь',
      'Июль',
      'Август',
      'Сентябрь',
      'Октябрь',
      'Ноябрь',
      'Декабрь',
    ];
    return months[month - 1];
  }

  TaskRequestParams copyWith({
    TaskViewType? viewType,
    int? page,
    int? month,
    int? year,
    String? clientId,
  }) {
    return TaskRequestParams(
      viewType: viewType ?? this.viewType,
      page: page ?? this.page,
      month: month ?? this.month,
      year: year ?? this.year,
      clientId: clientId ?? this.clientId,
    );
  }

  @override
  List<Object?> get props => [viewType, page, month, year, clientId];
}

/// Метаданные пагинации для бессрочных задач
class TaskPaginationMeta extends Equatable {
  final int totalTasks;
  final int currentPage;
  final int pageSize;
  final int totalPages;

  const TaskPaginationMeta({
    required this.totalTasks,
    required this.currentPage,
    required this.pageSize,
    required this.totalPages,
  });

  bool get hasNextPage => currentPage < totalPages - 1;
  bool get hasPreviousPage => currentPage > 0;

  factory TaskPaginationMeta.fromJson(Map<String, dynamic> json) {
    return TaskPaginationMeta(
      totalTasks: json['total_tasks'] ?? 0,
      currentPage: json['current_page'] ?? 0,
      pageSize: json['page_size'] ?? 100,
      totalPages: json['total_pages'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [totalTasks, currentPage, pageSize, totalPages];
}
