# test_sender.py

import os
import logging
import email_utils # Импортируем вашу утилиту как модуль

# --- 1. НАСТРОЙКА ПАРАМЕТРОВ ТЕСТА ---

# ВАЖНО: Укажите ваш email, который ПОДТВЕРЖДЕН в UniSender как адрес отправителя.
# Если этого не сделать, UniSender отклонит запрос.
SENDER_EMAIL_VERIFIED = "<EMAIL>"

# Укажите email, на который придет тестовое письмо.
# Вы должны иметь доступ к этому ящику.
RECIPIENT_EMAIL_TO_TEST = "<EMAIL>"

# Ваш API ключ для UniSender
API_KEY = "6t6s6yzpk4s1knaaj8gem7hkmazc5h4m67b1bcmy"

# --- 2. ИМИТАЦИЯ ПЕРЕМЕННЫХ ОКРУЖЕНИЯ YANDEX CLOUD ---
# Мы "обманываем" email_utils, заставляя его думать, что он запущен в облаке
# с настроенными переменными.
os.environ["UNISENDER_API_KEY"] = API_KEY
os.environ["SENDER_EMAIL"] = SENDER_EMAIL_VERIFIED

# --- 3. НАСТРОЙКА ЛОГИРОВАНИЯ ---
# Это позволит нам видеть информационные сообщения и ошибки из email_utils.py
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")


# --- 4. ОСНОВНАЯ ЧАСТЬ - ЗАПУСК ТЕСТА ---
if __name__ == "__main__":
    
    print("--- Запуск теста отправки письма через UniSender ---")
    
    # Проверка, что пользователь не забыл изменить email отправителя
    if "your-verified-email" in SENDER_EMAIL_VERIFIED:
        print("\n[!!!] ВНИМАНИЕ: Пожалуйста, отредактируйте файл test_sender.py и укажите ваш")
        print(f"      подтвержденный email в переменной SENDER_EMAIL_VERIFIED вместо '{SENDER_EMAIL_VERIFIED}'.\n")
    else:
        test_code = "987654" # Генерируем тестовый код
        
        print(f"Отправляем код '{test_code}' на адрес: {RECIPIENT_EMAIL_TO_TEST}...")
        
        # Вызываем функцию из вашего файла
        is_sent_successfully = email_utils.send_verification_code(
            email=RECIPIENT_EMAIL_TO_TEST,
            code=test_code
        )
        
        print("--- Тест завершен ---")
        
        # Выводим результат
        if is_sent_successfully:
            print(f"\n[✓] УСПЕХ: Запрос на отправку успешно принят UniSender.")
            print(f"      Проверьте почтовый ящик: {RECIPIENT_EMAIL_TO_TEST}")
        else:
            print(f"\n[✗] ОШИБКА: Не удалось отправить письмо.")
            print(f"      Проверьте вывод в консоли выше на наличие сообщений об ошибках.")