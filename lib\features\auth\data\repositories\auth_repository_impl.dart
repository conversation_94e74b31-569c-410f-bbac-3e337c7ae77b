import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/core/error/exception.dart';
import 'package:balansoved_enterprise/features/auth/data/data_source/auth_local_data_source.dart';
import 'package:balansoved_enterprise/features/auth/data/data_source/auth_remote_data_source.dart';
import 'package:balansoved_enterprise/features/auth/domain/entities/user_entity.dart';
import 'package:balansoved_enterprise/features/auth/domain/repositories/auth_repository.dart';

class AuthRepositoryImpl implements IAuthRepository {
  final IAuthRemoteDataSource remote;
  final IAuthLocalDataSource local;

  AuthRepositoryImpl({required this.remote, required this.local});

  @override
  Future<Either<Failure, Unit>> registerRequest({
    required String email,
    required String password,
    required String userName,
  }) async {
    try {
      debugPrint('🔵 [REPO] Начинаем регистрацию пользователя: $email');
      await remote.registerRequest(
        email: email,
        password: password,
        userName: userName,
      );
      debugPrint('✅ [REPO] Регистрация завершена успешно');
      return const Right(unit);
    } on ServerException catch (e) {
      debugPrint('❌ [REPO] Ошибка сервера при регистрации:\n$e');
      return Left(ServerFailure(message: e.message, details: e.toString()));
    } on NetworkException catch (e) {
      debugPrint('❌ [REPO] Сетевая ошибка при регистрации:\n$e');
      return Left(NetworkFailure(message: e.message, details: e.toString()));
    } catch (e, stackTrace) {
      debugPrint('❌ [REPO] Неожиданная ошибка при регистрации: $e');
      debugPrint('Stack Trace: $stackTrace');
      return Left(
        UnexpectedFailure(
          message: e.toString(),
          details: stackTrace.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> login({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('🔵 [REPO] Начинаем авторизацию пользователя: $email');
      final token = await remote.login(email: email, password: password);
      debugPrint('🔵 [REPO] Токен получен, сохраняем локально');
      await local.cacheAccessToken(token);
      debugPrint('✅ [REPO] Авторизация завершена успешно');
      return const Right(unit);
    } on ServerException catch (e) {
      debugPrint('❌ [REPO] Ошибка сервера при авторизации:\n$e');
      return Left(ServerFailure(message: e.message, details: e.toString()));
    } on NetworkException catch (e) {
      debugPrint('❌ [REPO] Сетевая ошибка при авторизации:\n$e');
      return Left(NetworkFailure(message: e.message, details: e.toString()));
    } on CacheException catch (e) {
      debugPrint('❌ [REPO] Ошибка кэша при авторизации:\n$e');
      return Left(CacheFailure(message: e.message, details: e.toString()));
    } catch (e, stackTrace) {
      debugPrint('❌ [REPO] Неожиданная ошибка при авторизации: $e');
      debugPrint('Stack Trace: $stackTrace');
      return Left(
        UnexpectedFailure(
          message: e.toString(),
          details: stackTrace.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> refreshToken({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('🔵 [REPO] Начинаем обновление токена для: $email');
      final token = await remote.refreshToken(email: email, password: password);
      debugPrint('🔵 [REPO] Новый токен получен, сохраняем локально');
      await local.cacheAccessToken(token);
      debugPrint('✅ [REPO] Обновление токена завершено успешно');
      return const Right(unit);
    } on ServerException catch (e) {
      debugPrint('❌ [REPO] Ошибка сервера при обновлении токена:\n$e');
      return Left(ServerFailure(message: e.message, details: e.toString()));
    } on NetworkException catch (e) {
      debugPrint('❌ [REPO] Сетевая ошибка при обновлении токена:\n$e');
      return Left(NetworkFailure(message: e.message, details: e.toString()));
    } on CacheException catch (e) {
      debugPrint('❌ [REPO] Ошибка кэша при обновлении токена:\n$e');
      return Left(CacheFailure(message: e.message, details: e.toString()));
    } catch (e, stackTrace) {
      debugPrint('❌ [REPO] Неожиданная ошибка при обновлении токена: $e');
      debugPrint('Stack Trace: $stackTrace');
      return Left(
        UnexpectedFailure(
          message: e.toString(),
          details: stackTrace.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> signOut() async {
    try {
      debugPrint('🔵 [REPO] Начинаем выход из системы');
      await local.clearAccessToken();
      debugPrint('✅ [REPO] Выход из системы завершен успешно');
      return const Right(unit);
    } on CacheException catch (e) {
      debugPrint('❌ [REPO] Ошибка кэша при выходе:\n$e');
      return Left(CacheFailure(message: e.message, details: e.toString()));
    } catch (e, stackTrace) {
      debugPrint('❌ [REPO] Неожиданная ошибка при выходе: $e');
      debugPrint('Stack Trace: $stackTrace');
      return Left(
        CacheFailure(message: e.toString(), details: stackTrace.toString()),
      );
    }
  }

  @override
  Future<Either<Failure, Option<UserEntity>>> checkAuthStatus() async {
    try {
      debugPrint('🔵 [REPO] Проверяем статус авторизации');
      final token = await local.getAccessToken();
      if (token == null || token.isEmpty) {
        debugPrint('ℹ️ [REPO] Токен отсутствует, пользователь не авторизован');
        return const Right(None());
      }
      debugPrint('✅ [REPO] Токен найден, пользователь авторизован');
      // Если понадобится валидация токена на сервере, можно добавить вызов.
      return Right(Some(UserEntity(id: '', email: null, userName: null)));
    } on CacheException catch (e) {
      debugPrint('❌ [REPO] Ошибка кэша при проверке статуса:\n$e');
      return Left(CacheFailure(message: e.message, details: e.toString()));
    } catch (e, stackTrace) {
      debugPrint('❌ [REPO] Неожиданная ошибка при проверке статуса: $e');
      debugPrint('Stack Trace: $stackTrace');
      return Left(
        CacheFailure(message: e.toString(), details: stackTrace.toString()),
      );
    }
  }
}
