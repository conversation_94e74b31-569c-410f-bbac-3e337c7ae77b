
---
Идентификатор - d4e5ctmd3olk0kcj9hv0
Описание - 𐀪 Добавить существующего пользователя в указанную фирму как сотрудника

Точка входа - index.handler
Таймаут - 15 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя с правами `OWNER` или `ADMIN`.
	-> Тело запроса:
		- `firm_id` (string, **обязательно**): ID фирмы, в которую добавляется сотрудник.
		- `email` (string, **обязательно**): Email пользователя для добавления.
		- `roles` (list, необязательно): Список ролей, по умолчанию `["EMPLOYEE"]`.
Внутренняя работа:
	1. **Авторизация**: Проверяется JWT токен администратора.
	2. **Парсинг запроса**: Используется утилита `utils.request_parser` для извлечения `firm_id`, `email`, `roles`.
	3. **Поиск кандидата**: В `jwt-database` по `email` находится `user_id` и `user_name` будущего сотрудника.
	4. **Транзакция в `firms-database`**:
		- **Проверяются права администратора в рамках указанной `firm_id`**.
		- Проверяется, что кандидат еще не работает в этой фирме.
		- В таблицу `Users` добавляется (`INSERT`) новая запись, связывающая `user_id` кандидата с `firm_id`.
На выходе:
	-> `201 Created`: {"message": "Employee added successfully", "user_id": "..."}
	-> `400 Bad Request`: Ошибка в теле запроса.
	-> `401/403`: Невалидный токен или недостаточно прав.
	-> `404 Not Found`: Если пользователь с таким `email` не найден в системе.
	-> `409 Conflict`: Если этот пользователь уже работает в данной фирме.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT`, `YDB_DATABASE` (для `jwt-database`)
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`

---

```python
import json
import os
import logging
import ydb
from utils import auth_utils, ydb_utils, request_parser

logging.basicConfig(level=logging.INFO)

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass

def handler(event, context):
    try:
        # 1. Авторизация
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '): raise AuthError("Unauthorized")
        
        token = auth_header.split(' ')[1]
        admin_payload = auth_utils.verify_jwt(token)
        if not admin_payload or 'user_id' not in admin_payload: raise AuthError("Invalid token")
        admin_user_id = admin_payload['user_id']

        # 2. Получение данных из запроса
        try:
            data = request_parser.parse_request_body(event)
        except ValueError as e:
            raise LogicError(str(e))

        firm_id = data.get('firm_id')
        new_email = data.get('email')
        new_roles = data.get('roles', ["EMPLOYEE"])

        if not all([firm_id, new_email]):
            raise LogicError("firm_id and email of the user to add are required.")

        # 3. Поиск пользователя в основной базе auth-data
        auth_driver = ydb_utils.get_ydb_driver()
        auth_pool = ydb.SessionPool(auth_driver)
        
        def find_user_by_email(session):
            query_text = f"PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8; SELECT user_id, user_name FROM users WHERE email = $email AND is_active = true;"
            query = session.prepare(query_text)
            result_sets = session.transaction(ydb.SerializableReadWrite()).execute(query, {'$email': new_email}, commit_tx=True)
            if not result_sets[0].rows: return None
            return result_sets[0].rows[0]

        target_user_data = auth_pool.retry_operation_sync(find_user_by_email)

        if not target_user_data:
            raise NotFoundError("User to be added not found or is not active.")
        
        target_user_id = target_user_data.user_id
        target_user_name = target_user_data.user_name

        # 4. Добавление пользователя в фирму в firms-database
        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)

        def add_employee_transaction(session):
            tx = session.transaction(ydb.SerializableReadWrite())

            admin_query = session.prepare("DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; SELECT roles FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;")
            admin_res = tx.execute(admin_query, {'$user_id': admin_user_id, '$firm_id': firm_id})
            
            if not admin_res[0].rows:
                raise AuthError("Requesting user is not a member of the specified firm.")
            
            admin_roles = json.loads(admin_res[0].rows[0].roles)
            if "ADMIN" not in admin_roles and "OWNER" not in admin_roles:
                raise AuthError("Insufficient permissions.")
            
            check_query = session.prepare("DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; SELECT 1 FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;")
            check_res = tx.execute(check_query, {'$user_id': target_user_id, '$firm_id': firm_id})
            if check_res[0].rows:
                raise LogicError("This user is already a member of this firm.")

            insert_query = session.prepare("""
                DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; DECLARE $email AS Utf8;
                DECLARE $full_name AS Utf8; DECLARE $roles AS Json;
                INSERT INTO Users (user_id, firm_id, email, password_hash, full_name, roles, is_active, created_at)
                VALUES ($user_id, $firm_id, $email, NULL, $full_name, $roles, true, CurrentUtcTimestamp());
            """)
            tx.execute(insert_query, {
                '$user_id': target_user_id, '$firm_id': firm_id, '$email': new_email,
                '$full_name': target_user_name, '$roles': json.dumps(new_roles)
            })
            
            tx.commit()
            return {"status": 201, "user_id": target_user_id}

        result = firms_pool.retry_operation_sync(add_employee_transaction)
        
        return {"statusCode": result['status'], "body": json.dumps({"message": "Employee added successfully", "user_id": result['user_id']})}

    except AuthError as e:
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 409, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error creating employee: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```

---
#### Запрос для тестирования (ИЗМЕНЕН)
```json
{
  "httpMethod": "POST",
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.q0nvV5-Nst5Zim-1IR4C0G16eYZEGdnfIAY6a1kf2qs"
  },
  "body": "{\"firm_id\": \"9a33483b-dfad-44a3-a36d-102b498ec0ef\", \"email\": \"<EMAIL>\", \"roles\": [\"EMPLOYEE\"]}"
}
```