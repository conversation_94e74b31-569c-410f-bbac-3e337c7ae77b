// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'router.dart';

/// generated route for
/// [CalendarPage]
class CalendarRoute extends PageRouteInfo<CalendarRouteArgs> {
  CalendarRoute({
    Key? key,
    required int initialYear,
    required int initialMonth,
    double? initialScrollOffset,
    List<PageRouteInfo>? children,
  }) : super(
         CalendarRoute.name,
         args: CalendarRouteArgs(
           key: key,
           initialYear: initialYear,
           initialMonth: initialMonth,
           initialScrollOffset: initialScrollOffset,
         ),
         rawPathParams: {'year': initialYear, 'month': initialMonth},
         rawQueryParams: {'scrollOffset': initialScrollOffset},
         initialChildren: children,
       );

  static const String name = 'CalendarRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final queryParams = data.queryParams;
      final args = data.argsAs<CalendarRouteArgs>(
        orElse:
            () => CalendarRouteArgs(
              initialYear: pathParams.getInt('year'),
              initialMonth: pathParams.getInt('month'),
              initialScrollOffset: queryParams.optDouble('scrollOffset'),
            ),
      );
      return CalendarPage(
        key: args.key,
        initialYear: args.initialYear,
        initialMonth: args.initialMonth,
        initialScrollOffset: args.initialScrollOffset,
      );
    },
  );
}

class CalendarRouteArgs {
  const CalendarRouteArgs({
    this.key,
    required this.initialYear,
    required this.initialMonth,
    this.initialScrollOffset,
  });

  final Key? key;

  final int initialYear;

  final int initialMonth;

  final double? initialScrollOffset;

  @override
  String toString() {
    return 'CalendarRouteArgs{key: $key, initialYear: $initialYear, initialMonth: $initialMonth, initialScrollOffset: $initialScrollOffset}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CalendarRouteArgs) return false;
    return key == other.key &&
        initialYear == other.initialYear &&
        initialMonth == other.initialMonth &&
        initialScrollOffset == other.initialScrollOffset;
  }

  @override
  int get hashCode =>
      key.hashCode ^
      initialYear.hashCode ^
      initialMonth.hashCode ^
      initialScrollOffset.hashCode;
}

/// generated route for
/// [CalendarRedirectPage]
class CalendarRedirectRoute extends PageRouteInfo<void> {
  const CalendarRedirectRoute({List<PageRouteInfo>? children})
    : super(CalendarRedirectRoute.name, initialChildren: children);

  static const String name = 'CalendarRedirectRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CalendarRedirectPage();
    },
  );
}

/// generated route for
/// [ClientsPage]
class ClientsRoute extends PageRouteInfo<void> {
  const ClientsRoute({List<PageRouteInfo>? children})
    : super(ClientsRoute.name, initialChildren: children);

  static const String name = 'ClientsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ClientsPage();
    },
  );
}

/// generated route for
/// [EmployeesPage]
class EmployeesRoute extends PageRouteInfo<void> {
  const EmployeesRoute({List<PageRouteInfo>? children})
    : super(EmployeesRoute.name, initialChildren: children);

  static const String name = 'EmployeesRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const EmployeesPage();
    },
  );
}

/// generated route for
/// [EmptyRouterPage]
class EmptyRouterRoute extends PageRouteInfo<void> {
  const EmptyRouterRoute({List<PageRouteInfo>? children})
    : super(EmptyRouterRoute.name, initialChildren: children);

  static const String name = 'EmptyRouterRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const EmptyRouterPage();
    },
  );
}

/// generated route for
/// [HomePage]
class HomeRoute extends PageRouteInfo<void> {
  const HomeRoute({List<PageRouteInfo>? children})
    : super(HomeRoute.name, initialChildren: children);

  static const String name = 'HomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const HomePage();
    },
  );
}

/// generated route for
/// [LoginPage]
class LoginRoute extends PageRouteInfo<void> {
  const LoginRoute({List<PageRouteInfo>? children})
    : super(LoginRoute.name, initialChildren: children);

  static const String name = 'LoginRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const LoginPage();
    },
  );
}

/// generated route for
/// [ProfilePage]
class ProfileRoute extends PageRouteInfo<void> {
  const ProfileRoute({List<PageRouteInfo>? children})
    : super(ProfileRoute.name, initialChildren: children);

  static const String name = 'ProfileRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ProfilePage();
    },
  );
}

/// generated route for
/// [TasksPage]
class TasksRoute extends PageRouteInfo<TasksRouteArgs> {
  TasksRoute({
    Key? key,
    TaskRequestParams? initialParams,
    String? initialTaskId,
    List<PageRouteInfo>? children,
  }) : super(
         TasksRoute.name,
         args: TasksRouteArgs(
           key: key,
           initialParams: initialParams,
           initialTaskId: initialTaskId,
         ),
         initialChildren: children,
       );

  static const String name = 'TasksRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TasksRouteArgs>(
        orElse: () => const TasksRouteArgs(),
      );
      return TasksPage(
        key: args.key,
        initialParams: args.initialParams,
        initialTaskId: args.initialTaskId,
      );
    },
  );
}

class TasksRouteArgs {
  const TasksRouteArgs({this.key, this.initialParams, this.initialTaskId});

  final Key? key;

  final TaskRequestParams? initialParams;

  final String? initialTaskId;

  @override
  String toString() {
    return 'TasksRouteArgs{key: $key, initialParams: $initialParams, initialTaskId: $initialTaskId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! TasksRouteArgs) return false;
    return key == other.key &&
        initialParams == other.initialParams &&
        initialTaskId == other.initialTaskId;
  }

  @override
  int get hashCode =>
      key.hashCode ^ initialParams.hashCode ^ initialTaskId.hashCode;
}

/// generated route for
/// [UnauthorizedPage]
class UnauthorizedRoute extends PageRouteInfo<void> {
  const UnauthorizedRoute({List<PageRouteInfo>? children})
    : super(UnauthorizedRoute.name, initialChildren: children);

  static const String name = 'UnauthorizedRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const UnauthorizedPage();
    },
  );
}

/// generated route for
/// [WelcomePage]
class WelcomeRoute extends PageRouteInfo<void> {
  const WelcomeRoute({List<PageRouteInfo>? children})
    : super(WelcomeRoute.name, initialChildren: children);

  static const String name = 'WelcomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const WelcomePage();
    },
  );
}
