Идентификатор - d4ep4vks5f5ahnbb6i45
Описание - 𐀪 Создать новую фирму и все связанные с ней таблицы

Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен любого зарегистрированного пользователя.
	-> `firm_name`: Название новой фирмы в теле запроса.
Внутренняя работа:
	1. **Авторизация**: Проверяется JWT токен. Из него извлекается `user_id` и `email` будущего владельца.
	2. **Парсинг запроса**: Используется утилита `utils.request_parser` для извлечения `firm_name`.
	3. **Генерация ID**: Создается новый уникальный `firm_id` (UUID).
	4. **Транзакция в `firms-database`**:
		- В таблицу `Firms` добавляется запись о новой фирме.
		- В таблице `Users` существующая запись пользователя обновляется: ему присваивается `firm_id` и роль `["OWNER"]`.
	5. **Создание таблицы клиентов**: Подключается к `clients-database` и создает таблицу `clients_[firm_id]`.
	6. **Создание таблицы задач**: Подключается к `tasks-database` и создает таблицу `tasks_[firm_id]`.
На выходе:
	-> `201 Created`: {"message": "Firm created successfully", "firm_id": "..."}
	-> `400 Bad Request`: Ошибка в теле запроса.
	-> `401 Unauthorized`: Если токен невалиден.
	-> `409 Conflict`: Пользователь уже является членом другой фирмы.
	-> `500 Internal Server Error`: В случае ошибки создания таблиц.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `YDB_ENDPOINT_CLIENTS`, `YDB_DATABASE_CLIENTS`
	- `YDB_ENDPOINT_TASKS`, `YDB_DATABASE_TASKS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`

---

```python
import json, os, uuid, datetime, pytz, logging
import ydb
from utils import auth_utils, ydb_utils, request_parser

logging.basicConfig(level=logging.INFO)

class LogicError(Exception): pass
class NotFoundError(Exception): pass

def handler(event, context):
    auth_header = event.get('headers', {}).get('Authorization', '')
    if not auth_header.startswith('Bearer '):
        return {"statusCode": 401, "body": json.dumps({"message": "Unauthorized"})}
    
    token = auth_header.split(' ')[1]
    user_payload = auth_utils.verify_jwt(token)
    if not user_payload or 'user_id' not in user_payload:
        return {"statusCode": 401, "body": json.dumps({"message": "Invalid token"})}

    try:
        data = request_parser.parse_request_body(event)
    except ValueError as e:
        logging.error(f"Request body processing error: {e}")
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}

    firm_name = data.get('firm_name')
    if not firm_name:
        return {"statusCode": 400, "body": json.dumps({"message": "firm_name is required."})}

    new_firm_id = str(uuid.uuid4())
    owner_user_id = user_payload['user_id']
    owner_email = user_payload['email']
    owner_user_name = "Unknown" # Значение по умолчанию

    try:
        auth_driver = ydb_utils.get_ydb_driver() # Используем основную БД
        def get_user_name(session):
            query_text = "DECLARE $user_id AS Utf8; SELECT user_name FROM users WHERE user_id = $user_id;"
            result = session.transaction(ydb.SerializableReadWrite()).execute(
                session.prepare(query_text), {"$user_id": owner_user_id}, commit_tx=True
            )
            if not result[0].rows:
                raise NotFoundError("Owner user not found in main auth database.")
            return result[0].rows[0].user_name
        
        owner_user_name = ydb.SessionPool(auth_driver).retry_operation_sync(get_user_name)

    except Exception as e:
        logging.error(f"Не удалось получить имя пользователя {owner_user_id} из основной БД: {e}", exc_info=True)

    try:
        firms_driver = ydb_utils.get_driver_for_db(
            os.environ["YDB_ENDPOINT_FIRMS"],
            os.environ["YDB_DATABASE_FIRMS"]
        )
        pool = ydb.SessionPool(firms_driver)

        def create_firm_and_assign_owner(session):
            tx = session.transaction(ydb.SerializableReadWrite())
            now = datetime.datetime.now(pytz.utc)
            
            check_query_text = "DECLARE $user_id AS Utf8; SELECT firm_id FROM Users WHERE user_id = $user_id;"
            res = tx.execute(session.prepare(check_query_text), {"$user_id": owner_user_id})
            if res[0].rows and res[0].rows[0].firm_id is not None:
                raise LogicError("User is already a member of a firm.")

            create_firm_query_text = """
                DECLARE $firm_id AS Utf8; DECLARE $firm_name AS Utf8; DECLARE $owner_user_id AS Utf8;
                DECLARE $created_at AS Timestamp; DECLARE $is_active AS Bool;
                INSERT INTO Firms (firm_id, firm_name, owner_user_id, created_at, is_active) 
                VALUES ($firm_id, $firm_name, $owner_user_id, $created_at, $is_active);
            """
            tx.execute(session.prepare(create_firm_query_text), {
                "$firm_id": new_firm_id, "$firm_name": firm_name, "$owner_user_id": owner_user_id,
                "$created_at": now, "$is_active": True
            })

            create_owner_query_text = """
                DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; DECLARE $email AS Utf8;
                DECLARE $full_name AS Utf8; DECLARE $roles AS Json; DECLARE $is_active AS Bool;
                DECLARE $created_at AS Timestamp;
                INSERT INTO Users (user_id, firm_id, email, full_name, roles, is_active, created_at)
                VALUES ($user_id, $firm_id, $email, $full_name, $roles, $is_active, $created_at);
            """
            tx.execute(session.prepare(create_owner_query_text), {
                "$user_id": owner_user_id,
                "$firm_id": new_firm_id,
                "$email": owner_email,
                "$full_name": owner_user_name,
                "$roles": json.dumps(["OWNER"]),
                "$is_active": True,
                "$created_at": now
            })
            tx.commit()
        
        pool.retry_operation_sync(create_firm_and_assign_owner)
        logging.info(f"Фирма {new_firm_id} и владелец {owner_user_id} успешно созданы/обновлены в firms-database.")
    
    except LogicError as e:
        return {"statusCode": 409, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Ошибка при работе с firms-database: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": f"Failed to create firm record: {e}"})}

    try:
        clients_db_path = os.environ["YDB_DATABASE_CLIENTS"]
        clients_table_path = os.path.join(clients_db_path, f"clients_{new_firm_id}")
        
        clients_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_CLIENTS"], clients_db_path)
        session = clients_driver.table_client.session().create()
        session.create_table(
            clients_table_path,
            ydb.TableDescription().with_primary_key("client_id").with_columns(
                ydb.Column("client_id", ydb.PrimitiveType.Utf8),
                ydb.Column("client_name", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("short_name", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("contacts_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("tax_and_legal_info_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("payment_schedule_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("patents_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("tags_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("comment", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("manual_creation_date", ydb.OptionalType(ydb.PrimitiveType.Date)),
                ydb.Column("is_active", ydb.OptionalType(ydb.PrimitiveType.Bool)),
                ydb.Column("created_at", ydb.OptionalType(ydb.PrimitiveType.Timestamp)),
                ydb.Column("updated_at", ydb.OptionalType(ydb.PrimitiveType.Timestamp)),
            )
        )
        logging.info(f"Таблица {clients_table_path} успешно создана.")

        tasks_db_path = os.environ["YDB_DATABASE_TASKS"]
        tasks_table_path = os.path.join(tasks_db_path, f"tasks_{new_firm_id}")

        tasks_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_TASKS"], tasks_db_path)
        session = tasks_driver.table_client.session().create()
        session.create_table(
            tasks_table_path,
            ydb.TableDescription().with_primary_key("task_id").with_columns(
                ydb.Column("task_id", ydb.PrimitiveType.Utf8),
                ydb.Column("title", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("description", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("client_ids_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("assignee_ids_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("observer_ids_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("creator_ids_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("status", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("priority", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("due_date", ydb.OptionalType(ydb.PrimitiveType.Timestamp)),
                ydb.Column("completed_at", ydb.OptionalType(ydb.PrimitiveType.Timestamp)),
                ydb.Column("attachments_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("checklist_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("reminders_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("recurrence_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("options_json", ydb.OptionalType(ydb.PrimitiveType.Json)),
                ydb.Column("holiday_transfer_rule", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("origin_task_id", ydb.OptionalType(ydb.PrimitiveType.Utf8)),
                ydb.Column("created_at", ydb.OptionalType(ydb.PrimitiveType.Timestamp)),
                ydb.Column("updated_at", ydb.OptionalType(ydb.PrimitiveType.Timestamp)),
            )
        )
        logging.info(f"Таблица {tasks_table_path} успешно создана.")

    except Exception as e:
        logging.error(f"Ошибка при создании таблиц для фирмы {new_firm_id}: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": f"Firm record created, but failed to create data tables. Please contact support. Firm ID: {new_firm_id}"})}

    return {"statusCode": 201, "body": json.dumps({"message": "Firm created successfully", "firm_id": new_firm_id})}
```

---

```
{
  "httpMethod": "POST",
  "headers": {
    "Content-Type": "application/json",
    "User-Agent": "Yandex-Cloud-Function-Test",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.PDzP6FKIJMH4jKzT4v8kjyzJQp0DsRa5qAW5hu1LEO4"
  },
  "multiValueHeaders": {
    "Content-Type": [
      "application/json"
    ],
    "User-Agent": [
      "Yandex-Cloud-Function-Test"
    ],
    "Authorization": [
      "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.PDzP6FKIJMH4jKzT4v8kjyzJQp0DsRa5qAW5hu1LEO4"
    ]
  },
  "requestContext": {
    "identity": {
      "sourceIp": "127.0.0.1",
      "userAgent": "Yandex-Cloud-Function-Test"
    },
    "httpMethod": "POST",
    "requestId": "test-request-id-create-firm-valid-token",
    "requestTimeEpoch": 1672522560
  },
  "body": "{\"firm_name\": \"Stas's Innovations\"}",
  "isBase64Encoded": false
}
```