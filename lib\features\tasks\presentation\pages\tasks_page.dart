import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_cubit.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/cubit/tasks_state.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/employees_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/task_create_card.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/widgets/task_detail_card.dart';

import 'package:balansoved_enterprise/features/tasks/presentation/widgets/task_filter_widget.dart';
import 'package:balansoved_enterprise/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:balansoved_enterprise/features/tariffs_and_storage/presentation/cubit/tariffs_and_storage_cubit.dart';

@RoutePage()
class TasksPage extends StatefulWidget {
  final TaskRequestParams? initialParams;
  final String? initialTaskId;

  const TasksPage({super.key, this.initialParams, this.initialTaskId});

  @override
  State<TasksPage> createState() => _TasksPageState();
}

class _TasksPageState extends State<TasksPage> {
  final TextEditingController _searchController = TextEditingController();
  String _filter = '';
  bool _creating = false;
  TaskEntity? _viewingTask;
  TaskEntity? _editingTask;

  int _sortColumnIndex = 0;
  bool _sortAsc = true;
  bool _onlyMy = false;

  late TaskRequestParams _currentParams;
  String? _initialTaskId;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() => _filter = _searchController.text);
    });

    _currentParams = widget.initialParams ?? TaskRequestParams.timeless();
    _initialTaskId = widget.initialTaskId;
  }

  @override
  void didUpdateWidget(covariant TasksPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialParams != null &&
        widget.initialParams != _currentParams) {
      setState(() {
        _currentParams = widget.initialParams!;
        _initialTaskId = widget.initialTaskId;
        _viewingTask = null;
        _editingTask = null;
        _creating = false;
      });
    } else if (widget.initialTaskId != null &&
        widget.initialTaskId != _initialTaskId) {
      setState(() {
        _initialTaskId = widget.initialTaskId;
        _viewingTask = null;
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _openTask(String taskId) {
    final tasks = context.read<TasksCubit>().tasks;
    final foundTask = tasks.where((t) => t.id == taskId);
    if (foundTask.isNotEmpty) {
      setState(() {
        _viewingTask = foundTask.first;
        _initialTaskId = null; // сбрасываем, чтобы не открывать повторно
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_initialTaskId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _openTask(_initialTaskId!);
      });
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    prefixIcon: Icon(Icons.search),
                    hintText: 'Поиск задач',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Builder(
                builder: (context) {
                  final tasksState = context.watch<TasksCubit>().state;
                  final firmState = context.watch<ActiveFirmCubit>().state;
                  final loading =
                      tasksState is TasksLoading || firmState.isLoading;
                  if (loading) {
                    return const LoadingTile(height: 24, width: 160);
                  }
                  return Row(
                    children: [
                      Checkbox(
                        value: _onlyMy,
                        onChanged:
                            (val) => setState(() => _onlyMy = val ?? false),
                      ),
                      const Text('Только мои'),
                    ],
                  );
                },
              ),
              const SizedBox(width: 16),
              _buildCreateButton(context),
            ],
          ),
          const SizedBox(height: 16),
          if (!_creating && _viewingTask == null && _editingTask == null)
            TaskFilterWidget(
              currentParams: _currentParams,
              paginationMeta: context.watch<TasksCubit>().paginationMeta,
              onParamsChanged: (params) {
                setState(() {
                  _currentParams = params;
                  _viewingTask = null;
                  _editingTask = null;
                });
              },
              onNextPage:
                  () => context.read<TasksCubit>().nextPage(
                    context.read<ActiveFirmCubit>().state.selectedFirm!.id,
                  ),
              onPreviousPage:
                  () => context.read<TasksCubit>().previousPage(
                    context.read<ActiveFirmCubit>().state.selectedFirm!.id,
                  ),
            ),
          Expanded(child: _buildBody()),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_creating) {
      return ListView(
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: TaskCreateCard(
              onCancel: () => setState(() => _creating = false),
              onCreated: (task) {
                final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
                if (firm != null) {
                  context.read<TasksCubit>().saveTask(firm.id, task);
                }
                setState(() => _creating = false);
              },
            ),
          ),
        ],
      );
    }
    if (_viewingTask != null) {
      return ListView(
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: TaskDetailCard(
              task: _viewingTask!,
              onClose: () => setState(() => _viewingTask = null),
              onEdit:
                  () => setState(() {
                    final tasks = context.read<TasksCubit>().tasks;
                    _editingTask = tasks.firstWhere(
                      (e) => e.id == _viewingTask!.id,
                      orElse: () => _viewingTask!,
                    );
                    _viewingTask = null;
                  }),
            ),
          ),
        ],
      );
    }
    if (_editingTask != null) {
      return ListView(
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: TaskCreateCard(
              initialTask: _editingTask,
              onCancel: () => setState(() => _editingTask = null),
              onCreated: (task) {
                final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
                if (firm != null) {
                  context.read<TasksCubit>().saveTask(firm.id, task);
                }
                setState(() => _editingTask = null);
              },
            ),
          ),
        ],
      );
    }
    return _TasksTableSection(
      key: ValueKey(_currentParams),
      filterProvider: () => _filter,
      sortColumnIndex: _sortColumnIndex,
      sortAsc: _sortAsc,
      onSort:
          (i, asc) => setState(() {
            _sortColumnIndex = i;
            _sortAsc = asc;
          }),
      onView: (task) => setState(() => _viewingTask = task),
      onEdit: (task) => setState(() => _editingTask = task),
      showOnlyMy: _onlyMy,
      currentParams: _currentParams,
    );
  }

  Widget _buildCreateButton(BuildContext context) {
    final firmState = context.watch<ActiveFirmCubit>().state;
    final employeesState = context.watch<EmployeesCubit>().state;
    final clientsState = context.watch<ClientsCubit>().state;
    final tasksState = context.watch<TasksCubit>().state;
    final isLoading =
        firmState.isLoading ||
        firmState.selectedFirm == null ||
        employeesState.isLoading ||
        clientsState.isLoading ||
        tasksState is TasksLoading;
    if (isLoading) {
      return const LoadingTile(height: 48, width: 160);
    }
    return ElevatedButton.icon(
      icon: const Icon(Icons.add_task),
      label: const Text('Создать задачу'),
      onPressed: () => setState(() => _creating = true),
    );
  }
}

class _TasksTableSection extends StatefulWidget {
  final String Function() filterProvider;
  final int sortColumnIndex;
  final bool sortAsc;
  final void Function(int, bool) onSort;
  final void Function(TaskEntity) onView;
  final void Function(TaskEntity) onEdit;
  final bool showOnlyMy;
  final TaskRequestParams currentParams;

  const _TasksTableSection({
    super.key,
    required this.filterProvider,
    required this.sortColumnIndex,
    required this.sortAsc,
    required this.onSort,
    required this.onView,
    required this.onEdit,
    required this.showOnlyMy,
    required this.currentParams,
  });

  @override
  State<_TasksTableSection> createState() => _TasksTableSectionState();
}

class _TasksTableSectionState extends State<_TasksTableSection> {
  final ScrollController _hController = ScrollController();

  @override
  void initState() {
    super.initState();
    final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
    if (firm != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<TasksCubit>().fetchTasks(firm.id, widget.currentParams);
      });
    }
  }

  @override
  void dispose() {
    _hController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ActiveFirmCubit, ActiveFirmState>(
      listener: (context, firmState) {
        if (!firmState.isLoading && firmState.selectedFirm != null) {
          final tasksState = context.read<TasksCubit>().state;
          if (tasksState is TasksInitial) {
            context.read<TasksCubit>().fetchTasks(
              firmState.selectedFirm!.id,
              widget.currentParams,
            );
          }
        }
      },
      child: BlocBuilder<ActiveFirmCubit, ActiveFirmState>(
        builder: (context, firmState) {
          if (firmState.isLoading || firmState.selectedFirm == null) {
            return const Center(child: CircularProgressIndicator());
          }
          return BlocBuilder<EmployeesCubit, EmployeesState>(
            builder: (context, employeesState) {
              if (employeesState.employees.isEmpty &&
                  !employeesState.isLoading) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  context.read<EmployeesCubit>().fetchEmployees(
                    firmState.selectedFirm!.id,
                  );
                });
              }
              return BlocBuilder<ClientsCubit, ClientsState>(
                builder: (context, clientsState) {
                  if (clientsState.clients.isEmpty && !clientsState.isLoading) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      context.read<ClientsCubit>().fetchClients(
                        firmState.selectedFirm!.id,
                      );
                      // Также загружаем информацию о хранилище для работы с файлами
                      context.read<TariffsAndStorageCubit>().loadStorageInfo(
                        firmState.selectedFirm!.id,
                      );
                    });
                  }
                  return BlocListener<TasksCubit, TasksState>(
                    listener: (context, state) {
                      if (state is TasksError) {
                        ScaffoldMessenger.of(
                          context,
                        ).showSnackBar(SnackBar(content: Text(state.message)));
                      }
                    },
                    child: BlocBuilder<TasksCubit, TasksState>(
                      builder: (context, tasksState) {
                        if (tasksState is TasksInitial) {
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            final firm =
                                context
                                    .read<ActiveFirmCubit>()
                                    .state
                                    .selectedFirm;
                            if (firm != null) {
                              context.read<TasksCubit>().fetchTasks(
                                firm.id,
                                widget.currentParams,
                              );
                            }
                          });
                        }

                        if (tasksState is TasksLoading ||
                            tasksState is TasksInitial ||
                            employeesState.isLoading ||
                            clientsState.isLoading) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }
                        if (tasksState is! TasksLoaded &&
                            tasksState is! TasksLoadingMore) {
                          return const Center(
                            child: Text('Нет данных о задачах'),
                          );
                        }

                        final allTasks =
                            tasksState is TasksLoaded
                                ? tasksState.tasks
                                : tasksState is TasksLoadingMore
                                ? tasksState.tasks
                                : <TaskEntity>[];

                        final search = widget.filterProvider().toLowerCase();
                        List<TaskEntity> data =
                            allTasks.where((t) {
                              final concat =
                                  '${t.title}${t.description ?? ''}${_translateStatus(t.status)}${_translatePriority(t.priority)}'
                                      .toLowerCase();
                              return search.isEmpty || concat.contains(search);
                            }).toList();

                        if (widget.showOnlyMy) {
                          final authState = context.watch<AuthCubit>().state;
                          if (authState is AuthInitial) {
                            context.read<AuthCubit>().checkAuth();
                          }
                          final profileState =
                              context.watch<ProfileCubit>().state;
                          if (profileState is ProfileInitial) {
                            context.read<ProfileCubit>().fetchProfile();
                          }
                          if (authState is! AuthAuthenticated ||
                              profileState is! ProfileLoaded) {
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          }
                          final employees = employeesState.employees;
                          final myEmployeeIds =
                              employees
                                  .where(
                                    (e) =>
                                        e.id == profileState.profile.id ||
                                        e.email == profileState.profile.email,
                                  )
                                  .map((e) => e.id)
                                  .toSet();
                          data =
                              data
                                  .where(
                                    (t) =>
                                        t.assigneeIds.any(
                                          myEmployeeIds.contains,
                                        ) ||
                                        t.creatorIds.any(
                                          myEmployeeIds.contains,
                                        ) ||
                                        t.observerIds.any(
                                          myEmployeeIds.contains,
                                        ),
                                  )
                                  .toList();
                        }

                        data.sort((a, b) {
                          int res;
                          switch (widget.sortColumnIndex) {
                            case 0:
                              res = a.title.compareTo(b.title);
                              break;
                            case 1:
                              res = _translateStatus(
                                a.status,
                              ).compareTo(_translateStatus(b.status));
                              break;
                            case 2:
                              res = _translatePriority(
                                a.priority,
                              ).compareTo(_translatePriority(b.priority));
                              break;
                            case 3:
                              final aDate = a.dueDate ?? DateTime(2099);
                              final bDate = b.dueDate ?? DateTime(2099);
                              res = aDate.compareTo(bDate);
                              break;
                            default:
                              res = 0;
                          }
                          return widget.sortAsc ? res : -res;
                        });

                        if (data.isEmpty) {
                          return const Center(child: Text('Нет задач'));
                        }

                        return Column(
                          children: [
                            Expanded(
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  return SizedBox.expand(
                                    child: Scrollbar(
                                      controller: _hController,
                                      thumbVisibility: true,
                                      trackVisibility: true,
                                      scrollbarOrientation:
                                          ScrollbarOrientation.bottom,
                                      child: SingleChildScrollView(
                                        controller: _hController,
                                        scrollDirection: Axis.horizontal,
                                        child: ConstrainedBox(
                                          constraints: BoxConstraints(
                                            minWidth: constraints.maxWidth,
                                          ),
                                          child: DataTable(
                                            showCheckboxColumn: false,
                                            sortColumnIndex:
                                                widget.sortColumnIndex,
                                            sortAscending: widget.sortAsc,
                                            columns: [
                                              DataColumn(
                                                label: const Text('Название'),
                                                onSort:
                                                    (i, asc) =>
                                                        widget.onSort(i, asc),
                                              ),
                                              DataColumn(
                                                label: const Text('Статус'),
                                                onSort:
                                                    (i, asc) =>
                                                        widget.onSort(i, asc),
                                              ),
                                              DataColumn(
                                                label: const Text('Приоритет'),
                                                onSort:
                                                    (i, asc) =>
                                                        widget.onSort(i, asc),
                                              ),
                                              DataColumn(
                                                label: const Text('Срок'),
                                                onSort:
                                                    (i, asc) =>
                                                        widget.onSort(i, asc),
                                              ),
                                              const DataColumn(
                                                label: Text('Действия'),
                                              ),
                                            ],
                                            rows:
                                                data
                                                    .map(
                                                      (t) => DataRow(
                                                        onSelectChanged:
                                                            (_) => widget
                                                                .onView(t),
                                                        cells: [
                                                          DataCell(
                                                            SizedBox(
                                                              width: 200,
                                                              child: Text(
                                                                t.title,
                                                                overflow:
                                                                    TextOverflow
                                                                        .ellipsis,
                                                                maxLines: 2,
                                                              ),
                                                            ),
                                                          ),
                                                          DataCell(
                                                            _StatusDropdownCell(
                                                              task: t,
                                                            ),
                                                          ),
                                                          DataCell(
                                                            Text(
                                                              _translatePriority(
                                                                t.priority,
                                                              ),
                                                            ),
                                                          ),
                                                          DataCell(
                                                            Text(
                                                              t.dueDate
                                                                      ?.toString()
                                                                      .substring(
                                                                        0,
                                                                        10,
                                                                      ) ??
                                                                  '–',
                                                            ),
                                                          ),
                                                          DataCell(
                                                            Row(
                                                              children: [
                                                                IconButton(
                                                                  icon: const Icon(
                                                                    Icons.edit,
                                                                  ),
                                                                  onPressed:
                                                                      () => widget
                                                                          .onEdit(
                                                                            t,
                                                                          ),
                                                                ),
                                                                IconButton(
                                                                  icon: const Icon(
                                                                    Icons
                                                                        .visibility,
                                                                  ),
                                                                  onPressed:
                                                                      () => widget
                                                                          .onView(
                                                                            t,
                                                                          ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    )
                                                    .toList(),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            if (widget.currentParams.viewType ==
                                TaskViewType.timeless)
                              BlocBuilder<TasksCubit, TasksState>(
                                builder: (context, state) {
                                  final cubit = context.read<TasksCubit>();
                                  if (!cubit.canLoadMore &&
                                      !cubit.isLoadingMore) {
                                    return const SizedBox.shrink();
                                  }
                                  return Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: SizedBox(
                                      width: double.infinity,
                                      child: ElevatedButton.icon(
                                        onPressed:
                                            cubit.isLoadingMore
                                                ? null
                                                : () {
                                                  context
                                                      .read<TasksCubit>()
                                                      .loadMoreTasks(
                                                        context
                                                            .read<
                                                              ActiveFirmCubit
                                                            >()
                                                            .state
                                                            .selectedFirm!
                                                            .id,
                                                      );
                                                },
                                        icon:
                                            cubit.isLoadingMore
                                                ? const SizedBox(
                                                  width: 16,
                                                  height: 16,
                                                  child:
                                                      CircularProgressIndicator(
                                                        strokeWidth: 2,
                                                      ),
                                                )
                                                : const Icon(Icons.expand_more),
                                        label: Text(
                                          cubit.isLoadingMore
                                              ? 'Загружаем...'
                                              : 'Загрузить ещё задач',
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                          ],
                        );
                      },
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  String _translateStatus(String status) {
    switch (status) {
      case 'in_progress':
      case 'ongoing':
        return 'Активна';
      case 'completed':
      case 'done':
        return 'Завершена';
      case 'cancelled':
        return 'Отменена';
      case 'pending':
        return 'Новая';
      case 'testing':
        return 'Тестирование';
      case 'blocked':
        return 'Заблокирована';
      default:
        return status;
    }
  }

  String _translatePriority(String priority) {
    switch (priority) {
      case 'low':
        return 'Низкий';
      case 'medium':
        return 'Средний';
      case 'high':
        return 'Высокий';
      case 'critical':
        return 'Критический';
      default:
        return priority;
    }
  }
}

/// Виджет для редактирования статуса задачи прямо в таблице
class _StatusDropdownCell extends StatelessWidget {
  final TaskEntity task;

  const _StatusDropdownCell({required this.task});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 120,
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: _InlineStatusDropdown(task: task),
    );
  }
}

/// Встроенный выпадающий список для редактирования статуса
class _InlineStatusDropdown extends StatefulWidget {
  final TaskEntity task;

  const _InlineStatusDropdown({required this.task});

  @override
  State<_InlineStatusDropdown> createState() => _InlineStatusDropdownState();
}

class _InlineStatusDropdownState extends State<_InlineStatusDropdown> {
  late String _currentStatus;
  bool _isUpdating = false;

  // Доступные статусы с переводом (только нужные)
  static const Map<String, String> _statuses = {
    'in_progress': 'Активна',
    'completed': 'Завершена',
    'cancelled': 'Отменена',
  };

  @override
  void initState() {
    super.initState();
    _currentStatus = _normalizeStatus(widget.task.status);
  }

  @override
  void didUpdateWidget(covariant _InlineStatusDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.task.status != widget.task.status) {
      _currentStatus = _normalizeStatus(widget.task.status);
    }
  }

  String _normalizeStatus(String status) {
    if (_statuses.containsKey(status)) return status;

    if (status.contains('progress') || status.contains('ongoing')) {
      return 'in_progress';
    } else if (status.contains('complet') || status.contains('done')) {
      return 'completed';
    } else if (status.contains('cancel')) {
      return 'cancelled';
    } else {
      return 'in_progress'; // По умолчанию
    }
  }

  String _translateStatus(String status) {
    return _statuses[status] ?? status;
  }

  @override
  Widget build(BuildContext context) {
    if (_isUpdating) {
      return SizedBox(
        width: 120,
        height: 32,
        child: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _translateStatus(_currentStatus),
                style: Theme.of(context).textTheme.bodyMedium,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      );
    }

    return DropdownButtonFormField<String>(
      value: _currentStatus,
      isDense: true,
      isExpanded: true,
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
      items:
          _statuses.entries
              .map(
                (e) => DropdownMenuItem<String>(
                  value: e.key,
                  child: Text(
                    e.value,
                    style: Theme.of(context).textTheme.bodyMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              )
              .toList(),
      onChanged: (newStatus) async {
        if (newStatus == null || newStatus == _currentStatus) return;

        setState(() => _isUpdating = true);

        final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
        if (firm == null) {
          setState(() => _isUpdating = false);
          return;
        }

        try {
          await context.read<TasksCubit>().updateTaskStatus(
            firm.id,
            widget.task.id,
            newStatus,
          );

          if (mounted) {
            setState(() {
              _currentStatus = newStatus;
              _isUpdating = false;
            });
          }
        } catch (e) {
          if (mounted) {
            setState(() => _isUpdating = false);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Ошибка обновления статуса: ${e.toString()}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
    );
  }
}
