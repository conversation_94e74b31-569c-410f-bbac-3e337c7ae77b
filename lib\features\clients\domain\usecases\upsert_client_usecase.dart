import 'package:balansoved_enterprise/core/error/failure.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/domain/repositories/clients_repository.dart';
import 'package:dartz/dartz.dart';

class UpsertClientUseCase {
  final IClientsRepository repository;
  const UpsertClientUseCase(this.repository);

  Future<Either<Failure, Unit>> call(String firmId, ClientEntity client) {
    return repository.upsertClient(firmId, client);
  }
}
