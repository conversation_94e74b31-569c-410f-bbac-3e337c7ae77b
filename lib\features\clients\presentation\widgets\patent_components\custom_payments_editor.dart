import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_input_field.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/edit_card_components/client_utils.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_picker_dialog.dart';

class CustomPaymentsEditor extends StatefulWidget {
  final List<PaymentDate> payments;
  final bool isEditing;
  final String title;
  final ValueChanged<List<PaymentDate>> onUpdate;

  const CustomPaymentsEditor({
    super.key,
    required this.payments,
    required this.isEditing,
    required this.title,
    required this.onUpdate,
  });

  @override
  State<CustomPaymentsEditor> createState() => _CustomPaymentsEditorState();
}

class _CustomPaymentsEditorState extends State<CustomPaymentsEditor> {
  late List<TextEditingController> _dateControllers;
  late List<FocusNode> _dateFocusNodes;

  @override
  void initState() {
    super.initState();
    _setupControllers();
  }

  @override
  void didUpdateWidget(CustomPaymentsEditor oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.payments.length != oldWidget.payments.length) {
      _disposeControllers();
      _setupControllers();
    }
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  void _setupControllers() {
    _dateControllers =
        widget.payments
            .map(
              (p) => TextEditingController(
                text: DateFormat('dd.MM.yyyy').format(p.date),
              ),
            )
            .toList();
    _dateFocusNodes = widget.payments.map((_) => FocusNode()).toList();
  }

  void _disposeControllers() {
    for (var controller in _dateControllers) {
      controller.dispose();
    }
    for (var focusNode in _dateFocusNodes) {
      focusNode.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (int i = 0; i < widget.payments.length; i++)
          _buildCustomPaymentRow(context, i, widget.payments[i]),
        const SizedBox(height: 8),
        if (widget.isEditing)
          TextButton.icon(
            icon: const Icon(Icons.add),
            label: Text('Добавить ${widget.title.toLowerCase()}'),
            onPressed: () {
              final newList = List<PaymentDate>.from(widget.payments)
                ..add(PaymentDate(date: DateTime.now(), amount: 0.0));
              widget.onUpdate(newList);
            },
          ),
      ],
    );
  }

  Widget _buildCustomPaymentRow(
    BuildContext context,
    int index,
    PaymentDate payment,
  ) {
    if (index >= _dateControllers.length) {
      return const SizedBox.shrink();
    }
    final amountText = payment.amount.toStringAsFixed(2);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            flex: 2,
            child: SmartDateInputField(
              controller: _dateControllers[index],
              focusNode: _dateFocusNodes[index],
              labelText: 'Дата',
              prefixIcon: Icons.calendar_today,
              readOnly: !widget.isEditing,
              enabled: widget.isEditing,
              onCalendarTap: () async {
                final newDate = await SmartDatePickerDialog.show(
                  context: context,
                  initialDate: payment.date,
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                  helpText: 'Выберите дату платежа',
                  allowClear: false,
                );
                if (newDate != null) {
                  final newList = List<PaymentDate>.from(widget.payments);
                  newList[index] = PaymentDate(
                    date: newDate,
                    amount: payment.amount,
                  );
                  widget.onUpdate(newList);
                }
                return newDate;
              },
              onDateChanged: (newDate) {
                if (newDate != null) {
                  final newList = List<PaymentDate>.from(widget.payments);
                  newList[index] = PaymentDate(
                    date: newDate,
                    amount: payment.amount,
                  );
                  widget.onUpdate(newList);
                }
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Введите дату';
                }
                // Валидация теперь встроена в SmartDateInputField
                return null;
              },
              fieldName: 'Дата',
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: TextFormField(
              key: ValueKey('${widget.title.toLowerCase()}_amount_$index'),
              initialValue: amountText,
              decoration: const InputDecoration(labelText: 'Сумма'),
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              readOnly: !widget.isEditing,
              onTap:
                  !widget.isEditing
                      ? () => ClientUtils.copyToClipboard(
                        context,
                        amountText,
                        'Сумма',
                      )
                      : null,
              onChanged:
                  widget.isEditing
                      ? (value) {
                        final newAmount = double.tryParse(value) ?? 0.0;
                        final newList = List<PaymentDate>.from(widget.payments);
                        newList[index] = PaymentDate(
                          date: payment.date,
                          amount: newAmount,
                        );
                        widget.onUpdate(newList);
                      }
                      : null,
            ),
          ),
          if (widget.isEditing)
            IconButton(
              icon: const Icon(Icons.delete_outline),
              onPressed: () {
                final newList = List<PaymentDate>.from(widget.payments)
                  ..removeAt(index);
                widget.onUpdate(newList);
              },
            ),
        ],
      ),
    );
  }
}
