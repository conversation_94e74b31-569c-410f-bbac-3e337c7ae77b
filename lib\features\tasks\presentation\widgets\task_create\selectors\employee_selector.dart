import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../employees_and_firms/presentation/cubit/employees_cubit.dart';
import '../../../../../employees_and_firms/domain/entities/employee_entity.dart';

class EmployeeSelector extends StatelessWidget {
  final String title;
  final List<String> selectedIds;
  final void Function(List<String>) onChanged;

  const EmployeeSelector({
    super.key,
    required this.title,
    required this.selectedIds,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EmployeesCubit, EmployeesState>(
      builder: (context, state) {
        if (state.isLoading) return _Loading(title: title);

        final selected =
            state.employees.where((e) => selectedIds.contains(e.id)).toList();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: () => _openDialog(context, state.employees),
                  icon: const Icon(Icons.add),
                  label: const Text('Выбрать'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (selected.isEmpty)
              const Text('Не выбраны', style: TextStyle(color: Colors.grey))
            else
              _SelectedList<EmployeeEntity>(
                items: selected,
                titleBuilder: (e) => e.userName ?? e.email ?? e.id,
                subtitleBuilder: (e) => e.email ?? '',
                onRemove: (e) {
                  final newIds = List<String>.from(selectedIds)..remove(e.id);
                  onChanged(newIds);
                },
              ),
          ],
        );
      },
    );
  }

  void _openDialog(BuildContext context, List<EmployeeEntity> all) {
    showDialog(
      context: context,
      builder:
          (_) => _EmployeeSelectionDialog(
            title: title,
            allEmployees: all,
            selectedIds: selectedIds,
            onApply: onChanged,
          ),
    );
  }
}

// ---- Dialog ----
class _EmployeeSelectionDialog extends StatefulWidget {
  final String title;
  final List<EmployeeEntity> allEmployees;
  final List<String> selectedIds;
  final void Function(List<String>) onApply;
  const _EmployeeSelectionDialog({
    required this.title,
    required this.allEmployees,
    required this.selectedIds,
    required this.onApply,
  });

  @override
  State<_EmployeeSelectionDialog> createState() =>
      _EmployeeSelectionDialogState();
}

class _EmployeeSelectionDialogState extends State<_EmployeeSelectionDialog> {
  String _search = '';
  int _sortIndex = 0;
  bool _asc = true;
  late List<String> _current;

  @override
  void initState() {
    super.initState();
    _current = List.from(widget.selectedIds);
  }

  @override
  Widget build(BuildContext context) {
    List<EmployeeEntity> data =
        widget.allEmployees.where((e) {
          final s = _search.toLowerCase();
          final name = e.userName ?? e.email ?? e.id;
          return name.toLowerCase().contains(s) ||
              (e.email?.toLowerCase().contains(s) ?? false);
        }).toList();

    data.sort((a, b) {
      int res;
      switch (_sortIndex) {
        case 0:
          res = (a.userName ?? a.email ?? a.id).compareTo(
            b.userName ?? b.email ?? b.id,
          );
          break;
        case 1:
          res = (a.email ?? '').compareTo(b.email ?? '');
          break;
        default:
          res = 0;
      }
      return _asc ? res : -res;
    });

    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Выбор: ${widget.title}',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Поиск',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (v) => setState(() => _search = v),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: DataTable(
                  sortColumnIndex: _sortIndex,
                  sortAscending: _asc,
                  columns: [
                    DataColumn(
                      label: const Text('Имя'),
                      onSort:
                          (i, asc) => setState(() {
                            _sortIndex = i;
                            _asc = asc;
                          }),
                    ),
                    DataColumn(
                      label: const Text('Email'),
                      onSort:
                          (i, asc) => setState(() {
                            _sortIndex = i;
                            _asc = asc;
                          }),
                    ),
                    const DataColumn(label: Text('')),
                  ],
                  rows:
                      data.map((e) {
                        final sel = _current.contains(e.id);
                        return DataRow(
                          cells: [
                            DataCell(
                              SizedBox(
                                width: 200,
                                child: Text(
                                  e.userName ?? e.email ?? e.id,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ),
                            ),
                            DataCell(Text(e.email ?? '')),
                            DataCell(
                              IconButton(
                                icon: Icon(
                                  sel ? Icons.remove_circle : Icons.add_circle,
                                  color: sel ? Colors.red : Colors.green,
                                ),
                                onPressed: () {
                                  setState(() {
                                    if (sel) {
                                      _current.remove(e.id);
                                    } else {
                                      _current.add(e.id);
                                    }
                                  });
                                },
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Отмена'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    widget.onApply(_current);
                    Navigator.pop(context);
                  },
                  child: const Text('Применить'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Reusable widgets
class _Loading extends StatelessWidget {
  final String title;
  const _Loading({required this.title});
  @override
  Widget build(BuildContext context) => Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
      const SizedBox(height: 8),
      const Text('Загрузка...'),
    ],
  );
}

class _SelectedList<T> extends StatelessWidget {
  final List<T> items;
  final String Function(T) titleBuilder;
  final String Function(T) subtitleBuilder;
  final void Function(T) onRemove;
  const _SelectedList({
    required this.items,
    required this.titleBuilder,
    required this.subtitleBuilder,
    required this.onRemove,
  });
  @override
  Widget build(BuildContext context) => Container(
    decoration: BoxDecoration(
      border: Border.all(color: Colors.grey.shade300),
      borderRadius: BorderRadius.circular(8),
    ),
    child: Column(
      children:
          items
              .map(
                (e) => ListTile(
                  title: Text(titleBuilder(e)),
                  subtitle: Text(subtitleBuilder(e)),
                  trailing: IconButton(
                    icon: const Icon(Icons.remove_circle_outline),
                    onPressed: () => onRemove(e),
                  ),
                ),
              )
              .toList(),
    ),
  );
}
