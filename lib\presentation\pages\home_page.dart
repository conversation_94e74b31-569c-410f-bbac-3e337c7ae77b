import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:balansoved_enterprise/features/auth/presentation/widgets/user_display_widget.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/widgets/active_firm_selector.dart';
import 'package:balansoved_enterprise/presentation/widgets/loading_tile.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/router.dart';
import 'package:balansoved_enterprise/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:balansoved_enterprise/features/auth/domain/entities/user_entity.dart';

@RoutePage()
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: const ActiveFirmSelector(),
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        actions: [
          BlocBuilder<ProfileCubit, ProfileState>(
            builder: (context, profileState) {
              if (profileState is ProfileLoaded) {
                final userEntity = UserEntity(
                  id: profileState.profile.id,
                  email: profileState.profile.email,
                  userName: profileState.profile.userName,
                );
                return UserDisplayWidget(user: userEntity);
              }
              if (profileState is ProfileLoading) {
                return const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.0),
                  child: LoadingTile(height: 32, maxWidth: 120),
                );
              }
              // Fallback for not authenticated yet
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Row(
        children: [
          // Левое меню
          SizedBox(
            width: 250,
            child: BlocBuilder<ActiveFirmCubit, ActiveFirmState>(
              builder: (context, firmState) {
                if (firmState.isLoading) {
                  return const Align(
                    alignment: Alignment.topLeft,
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: LoadingTile(height: 48, maxWidth: 200),
                    ),
                  );
                }
                return _SidebarMenu();
              },
            ),
          ),
          const VerticalDivider(width: 1),
          // Правая часть – содержимое выбранной страницы
          const Expanded(child: AutoRouter()),
        ],
      ),
    );
  }
}

/// Виджет бокового меню с отслеживанием активного маршрута
class _SidebarMenu extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Используем watch для прослушивания изменений в роутере
    final router = context.watchRouter;

    return ListView(
      children: [
        _buildMenuItem(
          context: context,
          icon: Icons.people,
          title: 'Сотрудники',
          isActive: router.isRouteActive(EmployeesRoute.name),
          onTap: () {
            context.router.navigate(const EmployeesRoute());
          },
        ),
        _buildMenuItem(
          context: context,
          icon: Icons.business,
          title: 'Клиенты',
          isActive: router.isRouteActive(ClientsRoute.name),
          onTap: () {
            context.router.navigate(const ClientsRoute());
          },
        ),
        _buildMenuItem(
          context: context,
          icon: Icons.task_alt,
          title: 'Задачи',
          isActive: router.isRouteActive(TasksRoute.name),
          onTap: () {
            context.router.navigate(TasksRoute());
          },
        ),
        _buildMenuItem(
          context: context,
          icon: Icons.calendar_month,
          title: 'Календарь',
          isActive: router.isRouteActive(CalendarRoute.name),
          onTap: () {
            context.router.navigate(const CalendarRedirectRoute());
          },
        ),
        // Здесь будут другие страницы
      ],
    );
  }

  Widget _buildMenuItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        color:
            isActive
                ? colorScheme.primaryContainer.withValues(alpha: 0.8)
                : Colors.transparent,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color:
              isActive ? colorScheme.onPrimaryContainer : colorScheme.onSurface,
        ),
        title: Text(
          title,
          style: TextStyle(
            color:
                isActive
                    ? colorScheme.onPrimaryContainer
                    : colorScheme.onSurface,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
        hoverColor: colorScheme.primaryContainer.withValues(alpha: 0.4),
      ),
    );
  }
}
