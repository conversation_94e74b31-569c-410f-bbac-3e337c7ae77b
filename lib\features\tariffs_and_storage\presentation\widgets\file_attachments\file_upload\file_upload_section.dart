import '../utils/file_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:file_picker/file_picker.dart';
import 'file_upload_zone.dart';
import '../models/file_attachment_models.dart';
import '../file_display/file_card_widget.dart';
import '../../../cubit/tariffs_and_storage_cubit.dart';
import '../../../cubit/tariffs_and_storage_state.dart';
import '../../../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';

class FileUploadSection extends StatefulWidget {
  final List<FileAttachmentItem> files;
  final Function(FileAttachmentItem) onAddFile;
  final Function(FileAttachmentItem) onRemoveFile;
  final Function(FileAttachmentItem) onUpdateFile;
  final VoidCallback? onAutoSave;
  final String? title;
  final List<String>? allowedExtensions;
  final int? maxFileSize;

  const FileUploadSection({
    super.key,
    required this.files,
    required this.onAddFile,
    required this.onRemoveFile,
    required this.onUpdateFile,
    this.onAutoSave,
    this.title,
    this.allowedExtensions,
    this.maxFileSize,
  });

  @override
  State<FileUploadSection> createState() => _FileUploadSectionState();
}

class _FileUploadSectionState extends State<FileUploadSection> {
  @override
  void initState() {
    super.initState();
    // Слушаем изменения состояния хранилища
    context.read<TariffsAndStorageCubit>().stream.listen((state) {
      if (mounted) {
        _handleStorageStateChange(state);
      }
    });
  }

  void _handleStorageStateChange(TariffsAndStorageState state) {
    if (state is FileUploadCompleted) {
      // Обновляем файл как успешно загруженный
      final fileIndex = widget.files.indexWhere(
        (file) =>
            file.name == state.fileName &&
            file.status == FileAttachmentStatus.uploading,
      );

      if (fileIndex >= 0) {
        final updatedFile = widget.files[fileIndex].copyWith(
          fileKey: state.fileKey,
          status: FileAttachmentStatus.uploaded,
          uploadProgress: 1.0,
        );
        widget.onUpdateFile(updatedFile);
        widget.onAutoSave?.call();
      }
    } else if (state is FileUploadFailed) {
      // Обновляем файл как неуспешно загруженный
      final fileIndex = widget.files.indexWhere(
        (file) =>
            file.name == state.fileName &&
            file.status == FileAttachmentStatus.uploading,
      );

      if (fileIndex >= 0) {
        final updatedFile = widget.files[fileIndex].copyWith(
          status: FileAttachmentStatus.failed,
          errorMessage: state.message,
        );
        widget.onUpdateFile(updatedFile);
      }
    } else if (state is FileUploadInProgress) {
      // Обновляем прогресс загрузки
      final fileIndex = widget.files.indexWhere(
        (file) =>
            file.name == state.fileName &&
            file.status == FileAttachmentStatus.uploading,
      );

      if (fileIndex >= 0) {
        final updatedFile = widget.files[fileIndex].copyWith(
          uploadProgress: state.progress,
        );
        widget.onUpdateFile(updatedFile);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Заголовок и кнопка
        _buildSectionHeader(),
        const SizedBox(height: 16),

        // Область для файлов с интегрированной зоной загрузки
        _buildFilesWithUploadZone(),

        const SizedBox(height: 8),

        // Информация о хранилище
        BlocBuilder<TariffsAndStorageCubit, TariffsAndStorageState>(
          builder: (context, state) {
            if (state is TariffsAndStorageLoaded) {
              return _buildStorageInfo(
                state.data.storageInfo.usedBytes,
                state.data.subscriptionInfo.quotaBytes,
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget _buildSectionHeader() {
    return Row(
      children: [
        Text(
          widget.title ?? 'Файловые вложения',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const Spacer(),
        ElevatedButton.icon(
          icon: const Icon(Icons.cloud_upload_outlined),
          label: const Text('Загрузить файлы'),
          onPressed: _pickFiles,
        ),
      ],
    );
  }

  Widget _buildFilesWithUploadZone() {
    // Сортируем файлы: сначала рабочие, потом с ошибками
    final sortedFiles = List<FileAttachmentItem>.from(widget.files);
    sortedFiles.sort((a, b) {
      if (a.status == FileAttachmentStatus.failed &&
          b.status != FileAttachmentStatus.failed) {
        return 1; // a (failed) идет после b
      }
      if (a.status != FileAttachmentStatus.failed &&
          b.status == FileAttachmentStatus.failed) {
        return -1; // a идет до b (failed)
      }
      return 0; // Сохраняем исходный порядок для остальных
    });

    return _buildDropZoneWithFiles(sortedFiles);
  }

  Widget _buildDropZoneWithFiles(List<FileAttachmentItem> sortedFiles) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 170),
      decoration: BoxDecoration(
        border: Border.all(
          color: theme.dividerColor,
          width: 2,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(8),
        color: theme.scaffoldBackgroundColor.withAlpha(50),
      ),
      child: Stack(
        children: [
          // Зона drag&drop на весь фон
          Positioned.fill(
            child: FileUploadZone(
              onFilesSelected: _onFilesSelected,
              allowedExtensions: widget.allowedExtensions,
              maxFileSize: widget.maxFileSize,
              height: double.infinity,
              hintText:
                  sortedFiles.isEmpty
                      ? 'Перетащите файлы сюда или нажмите для выбора'
                      : 'Перетащите файлы сюда для добавления',
            ),
          ),
          // Файлы поверх зоны
          if (sortedFiles.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Wrap(
                spacing: 8.0,
                runSpacing: 8.0,
                children:
                    sortedFiles.map((file) {
                      Widget card;
                      if (file.status == FileAttachmentStatus.failed) {
                        card = _buildFailedFileCard(file);
                      } else {
                        card = FileCardWidget(
                          fileData: file.toMap(),
                          showRemoveButton: true,
                          onRemove: () => widget.onRemoveFile(file),
                        );
                      }
                      return SizedBox(width: 145, height: 170, child: card);
                    }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFailedFileCard(FileAttachmentItem file) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border.all(color: theme.colorScheme.error.withOpacity(0.5)),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Stack(
        children: [
          // File Icon and Name
          Padding(
            padding: const EdgeInsets.all(6.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  flex: 3,
                  child: Center(child: FileUtils.getFileIcon(file.name)),
                ),
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        file.name,
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w500,
                          fontSize: 11,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                      if (file.fileSize != null) ...[
                        const SizedBox(height: 1),
                        Text(
                          FileUtils.formatFileSize(file.fileSize!),
                          style: theme.textTheme.labelSmall?.copyWith(
                            fontSize: 9,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Error icon bottom left
          Positioned(
            left: 4,
            bottom: 4,
            child: Tooltip(
              message: file.errorMessage ?? 'Неизвестная ошибка',
              child: Icon(
                Icons.error_outline,
                color: theme.colorScheme.error,
                size: 16,
              ),
            ),
          ),

          // Retry and Delete buttons top right
          Positioned(
            top: 2,
            right: 2,
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => _retryUpload(file),
                  child: Container(
                    width: 18,
                    height: 18,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(9),
                    ),
                    child: Icon(
                      Icons.refresh,
                      size: 10,
                      color: theme.colorScheme.onPrimary,
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: () => widget.onRemoveFile(file),
                  child: Container(
                    width: 18,
                    height: 18,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.error.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(9),
                    ),
                    child: Icon(
                      Icons.close,
                      size: 10,
                      color: theme.colorScheme.onError,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageInfo(int usedBytes, int quotaBytes) {
    final theme = Theme.of(context);
    final usedPercentage = quotaBytes > 0 ? (usedBytes / quotaBytes) : 0.0;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.storage,
            size: 16,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Использовано хранилища: ${_formatFileSize(usedBytes)} из ${_formatFileSize(quotaBytes)}',
                  style: theme.textTheme.bodySmall,
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: usedPercentage.clamp(0.0, 1.0),
                  backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onFilesSelected(List<FileUploadItem> uploadItems) {
    final firmState = context.read<ActiveFirmCubit>().state;
    if (firmState.selectedFirm == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Не выбрана фирма')));
      return;
    }

    final storageCubit = context.read<TariffsAndStorageCubit>();

    for (final uploadItem in uploadItems) {
      // Добавляем файл в состояние ожидания
      final fileAttachment = FileAttachmentItem(
        name: uploadItem.name,
        fileSize: uploadItem.size,
        status: FileAttachmentStatus.uploading,
      );

      widget.onAddFile(fileAttachment);

      // Запускаем загрузку
      if (uploadItem.bytes != null) {
        storageCubit.uploadFile(
          firmId: firmState.selectedFirm!.id,
          fileName: uploadItem.name,
          fileBytes: uploadItem.bytes!,
        );
      }
    }

    widget.onAutoSave?.call();
  }

  void _retryUpload(FileAttachmentItem file) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Для повторной загрузки выберите файл заново'),
      ),
    );
    widget.onRemoveFile(file);
  }

  String _formatFileSize(int bytes) {
    if (bytes == 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB'];
    int unitIndex = 0;
    double size = bytes.toDouble();

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(unitIndex == 0 ? 0 : 1)} ${units[unitIndex]}';
  }

  Future<void> _pickFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.custom,
        allowedExtensions: widget.allowedExtensions,
      );

      if (result != null) {
        final files = <FileUploadItem>[];

        for (final file in result.files) {
          if (_validateFile(file.name, file.size)) {
            files.add(
              FileUploadItem(
                name: file.name,
                bytes: file.bytes,
                size: file.size,
              ),
            );
          }
        }

        if (files.isNotEmpty) {
          _onFilesSelected(files);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка выбора файлов: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  bool _validateFile(String fileName, int? fileSize) {
    // Проверка расширения
    if (widget.allowedExtensions != null) {
      final extension = fileName.split('.').last.toLowerCase();
      if (!widget.allowedExtensions!.contains(extension)) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Файл $fileName имеет неподдерживаемый формат'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
        return false;
      }
    }

    // Проверка размера
    if (widget.maxFileSize != null &&
        fileSize != null &&
        fileSize > widget.maxFileSize!) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Файл $fileName превышает максимальный размер ${_formatFileSize(widget.maxFileSize!)}',
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
      return false;
    }

    return true;
  }
}
