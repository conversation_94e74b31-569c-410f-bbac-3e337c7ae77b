import 'package:balansoved_enterprise/features/clients/domain/entities/client_entity.dart';
import 'package:balansoved_enterprise/features/clients/presentation/widgets/client_constants.dart';

/// Класс для управления состоянием ClientEditCard
class ClientEditState {
  // Основные поля
  DateTime? creationDate;
  String? ownershipForm;

  // Налоговые системы и отчётность
  List<String> taxSystems = [];
  List<String> profitTaxTypes = [];
  List<String> vatTypes = [];
  List<String> propertyTypes = [];
  String? reportingType;
  String? reportingOperator;
  List<String> exciseGoods = [];
  List<String> excisePaymentTerms = [];
  String? enpType;
  List<String> activityTypes = [];
  String? ndflType;

  // Дополнительные поля
  List<String> additionalTags = [];
  List<String> fixedContributionsIP = [];
  List<int> fixedContributionsPaymentDate = [];
  List<String> contributionsIP1Percent = [];
  List<int> contributionsIP1PercentPaymentDate = [];

  // Графики платежей
  PaymentSchedule? salaryPayment;
  bool salaryPaymentEnabled = false;
  PaymentSchedule? advancePayment;
  PaymentSchedule? ndflPayment;

  // Связанные сущности
  List<ContactEntity> contacts = [];
  List<PatentEntity> patents = [];

  // Режим редактирования
  bool isEditing = false;

  /// Инициализирует состояние данными из клиента
  void initializeFromClient(ClientEntity client) {
    creationDate = client.creationDate;
    fixedContributionsPaymentDate = client.fixedContributionsPaymentDate;

    // Фильтруем данные по доступным константам
    ownershipForm =
        ClientConstants.ownershipForms.contains(client.ownershipForm)
            ? client.ownershipForm
            : null;

    taxSystems =
        client.taxSystems
            .where((item) => ClientConstants.taxSystems.contains(item))
            .toList();

    profitTaxTypes.clear();
    profitTaxTypes.addAll(
      client.profitTaxTypes
          .where((item) => ClientConstants.profitTaxTypes.contains(item))
          .toList(),
    );

    vatTypes =
        client.vatTypes
            .where((item) => ClientConstants.vatTypes.contains(item))
            .toList();

    propertyTypes =
        client.propertyTypes
            .where((item) => ClientConstants.propertyTypes.contains(item))
            .toList();

    reportingType =
        ClientConstants.reportingTypes.contains(client.reportingType)
            ? client.reportingType
            : null;

    reportingOperator =
        ClientConstants.reportingOperators.contains(client.reportingOperator)
            ? client.reportingOperator
            : null;

    exciseGoods =
        client.exciseGoods
            .where((item) => ClientConstants.exciseGoods.contains(item))
            .toList();

    excisePaymentTerms =
        client.excisePaymentTerms
            .where((item) => ClientConstants.excisePaymentTerms.contains(item))
            .toList();

    enpType =
        ClientConstants.enpTypes.contains(client.enpType)
            ? client.enpType
            : null;

    activityTypes =
        client.activityTypes
            .where((item) => ClientConstants.activityTypes.contains(item))
            .toList();

    ndflType =
        ClientConstants.ndflTypes.contains(client.ndflType)
            ? client.ndflType
            : null;

    additionalTags = List.from(client.additionalTags);

    fixedContributionsIP =
        client.fixedContributionsIP
            .where(
              (item) => ClientConstants.fixedContributionsIP.contains(item),
            )
            .toList();

    contributionsIP1Percent =
        client.contributionsIP1Percent
            .where(
              (item) => ClientConstants.contributionsIP1Percent.contains(item),
            )
            .toList();

    contributionsIP1PercentPaymentDate =
        client.contributionsIP1PercentPaymentDate;

    // Графики платежей
    salaryPayment = client.salaryPayment;
    salaryPaymentEnabled = client.salaryPaymentEnabled;
    advancePayment = client.advancePayment;
    ndflPayment = client.ndflPayment;

    // Связанные сущности
    contacts = List.from(client.contacts);
    patents = List.from(client.patents);
  }

  /// Создаёт ClientEntity из текущего состояния
  ClientEntity createClientEntity(
    String clientId,
    String name,
    String? inn,
    String? kpp,
    String? shortName,
    String? comment,
  ) {
    return ClientEntity(
      id: clientId,
      name: name,
      inn: inn,
      kpp: kpp,
      shortName: shortName,
      comment: comment,
      creationDate: creationDate,
      ownershipForm: ownershipForm,
      taxSystems: taxSystems,
      profitTaxTypes: profitTaxTypes,
      vatTypes: vatTypes,
      propertyTypes: propertyTypes,
      reportingType: reportingType,
      reportingOperator: reportingOperator,
      exciseGoods: exciseGoods,
      excisePaymentTerms: excisePaymentTerms,
      enpType: enpType,
      activityTypes: activityTypes,
      ndflType: ndflType,
      additionalTags: additionalTags,
      fixedContributionsIP: fixedContributionsIP,
      fixedContributionsPaymentDate: fixedContributionsPaymentDate,
      contributionsIP1Percent: contributionsIP1Percent,
      contributionsIP1PercentPaymentDate: contributionsIP1PercentPaymentDate,
      salaryPayment: salaryPayment,
      salaryPaymentEnabled: salaryPaymentEnabled,
      advancePayment: advancePayment,
      ndflPayment: ndflPayment,
      contacts: contacts,
      patents: patents,
    );
  }
}
