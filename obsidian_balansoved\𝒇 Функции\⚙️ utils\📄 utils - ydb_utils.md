**1. `get_ydb_driver() -> ydb.Driver` (Старая функция)**
- **Назначение**: Предоставляет драйвер для **основной** базы данных, параметры которой заданы через переменные `YDB_ENDPOINT` и `YDB_DATABASE`. Используется для обратной совместимости с функциями аутентификации.
- **На входе**: Нет.
- **На выходе**: Объект `ydb.Driver` для основной базы.

**2. `get_driver_for_db(endpoint: str, database: str) -> ydb.Driver` (Новая функция)**
- **Назначение**: Предоставляет драйвер для **любой** указанной базы данных, используя ее эндпоинт и путь. Кеширует созданные драйверы, чтобы не пересоздавать их при повторных вызовах с теми же параметрами.
- **На входе**:
	-> `endpoint`: Строка с эндпоинтом нужной базы данных.
	-> `database`: Строка с путем к нужной базе данных.
- **Внутренняя работа**:
	1. Создает уникальный ключ для кеша на основе эндпоинта и пути.
	2. Проверяет, есть ли уже созданный драйвер по этому ключу.
	3. Если драйвер найден, возвращает его.
	4. Если нет, создает новый экземпляр драйвера, сохраняет его в кеш и возвращает.
- **На выходе**:
	-> Объект `ydb.Driver`, готовый к использованию для указанной базы данных.

---

```python
# ydb_utils.py

import ydb
import ydb.iam
import os
import logging

# Кеш для хранения инстансов драйверов. Ключ - "endpoint|database"
_drivers_cache = {}
_main_driver_instance = None

def get_ydb_driver() -> ydb.Driver:
    """
    (Для обратной совместимости)
    Инициализирует и возвращает синглтон-драйвер для основной базы данных,
    заданной через переменные YDB_ENDPOINT и YDB_DATABASE.
    """
    global _main_driver_instance
    if _main_driver_instance is None:
        try:
            endpoint = os.environ["YDB_ENDPOINT"]
            database = os.environ["YDB_DATABASE"]
            _main_driver_instance = get_driver_for_db(endpoint, database)
        except KeyError as e:
            logging.error(f"Missing main environment variable: {e}")
            raise RuntimeError(f"Main YDB configuration (YDB_ENDPOINT, YDB_DATABASE) is incomplete.") from e
    return _main_driver_instance


def get_driver_for_db(endpoint: str, database: str) -> ydb.Driver:
    """
    (Новая функция)
    Инициализирует и возвращает кешированный драйвер для указанных эндпоинта и базы.
    """
    cache_key = f"{endpoint}|{database}"
    if cache_key in _drivers_cache:
        return _drivers_cache[cache_key]

    try:
        logging.info(f"Initializing YDB driver for: {database}")
        sa_key_path = os.environ.get("SA_KEY_FILE", "ydb_sa_key.json")
        credentials = ydb.iam.ServiceAccountCredentials.from_file(sa_key_path)
        driver_config = ydb.DriverConfig(endpoint, database, credentials=credentials)
        
        driver = ydb.Driver(driver_config)
        driver.wait(timeout=5)
        
        _drivers_cache[cache_key] = driver
        logging.info(f"Driver for {database} successfully initialized and cached.")
        return driver
        
    except Exception as e:
        logging.error(f"Failed to initialize YDB driver for {database}: {e}")
        raise RuntimeError(f"Could not connect to YDB for {database}.") from e

def clear_drivers_cache():
    """
    Принудительно очищает кеш драйверов YDB.
    Вызывается для предотвращения использования "протухших" соединений
    в "теплых" экземплярах функции.
    """
    global _drivers_cache
    global _main_driver_instance
    
    for driver in _drivers_cache.values():
        try:
            driver.stop(timeout=1)
        except Exception:
            pass # Игнорируем ошибки при остановке
            
    _drivers_cache.clear()
    _main_driver_instance = None
    logging.info("YDB drivers cache has been cleared.")
```