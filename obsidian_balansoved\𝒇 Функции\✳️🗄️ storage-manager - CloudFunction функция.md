
Идентификатор - d4e... (будет присвоен при создании)
Описание - 🗄️ Управляет файлами в Object Storage: выдает ссылки на загрузку и удаляет файлы.

Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен пользователя.
	-> Тело запроса:
		- `action` (string, **обязательно**): `GET_UPLOAD_URL` или `DELETE`.
		- `firm_id` (string, **обязательно**): ID фирмы, в контексте которой выполняется операция.
		- `filename` (string): Имя файла для загрузки (для `GET_UPLOAD_URL`).
		- `filesize` (integer): Размер файла в байтах (для `GET_UPLOAD_URL`).
		- `file_key` (string): Ключ файла в хранилище (для `DELETE`).

Внутренняя работа:
	1. **Координация (`index.py`)**:
		- Проверяет JWT, извлекает `user_id`.
		- Использует `request_parser` для парсинга тела запроса.
		- **Проверяет членство пользователя в указанной фирме (Требование №3)**.
	2. **Маршрутизация (`index.py`)**:
		- В зависимости от `action` вызывает соответствующий обработчик.
	3. **Выполнение операции**:
		- **`upload.py`**:
			- **Проверяет квоту хранилища фирмы с учетом нового файла (Требование №2)**.
			- **Генерирует ключ в формате `firm_id/год/месяц/оригинальное_имя_firm_id.расширение` (Требование №1)**.
			- **Возвращает клиенту pre-signed URL, позволяя загружать большие файлы (Требование №4)**.
		- **`delete.py`**:
			- **Проверяет, что `file_key` принадлежит фирме авторизованного пользователя (Требование №3)**.
			- Вызывает `storage_utils.delete_object`.

На выходе:
	-> `200 OK` (GET_UPLOAD_URL): `{"upload_url": "...", "file_key": "..."}`
	-> `200 OK` (DELETE): `{"message": "File deleted successfully"}`
	-> `400 Bad Request`, `403 Forbidden`, `413 Payload Too Large`, `500 Internal Server Error`.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`, `utils/storage_utils.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`
	- `STORAGE_BUCKET_NAME`: Имя вашего бакета в Object Storage.
	- `STORAGE_ENDPOINT`: `https://storage.yandexcloud.net`
	- `STORAGE_REGION`: `ru-central1`
	- `STORAGE_ACCESS_KEY`: Ключ доступа сервисного аккаунта.
	- `STORAGE_SECRET_KEY`: Секретный ключ сервисного аккаунта.

---
#### index.py
```python
import json, os, logging
import ydb
from utils import auth_utils, ydb_utils, request_parser
from upload import handle_get_upload_url
from delete import handle_delete
from custom_errors import AuthError, LogicError, QuotaExceededError

logging.basicConfig(level=logging.INFO)

def check_membership(session, user_id, firm_id):
    """Проверяет, является ли пользователь членом указанной фирмы."""
    query_text = "DECLARE $user_id AS Utf8; DECLARE $firm_id AS Utf8; SELECT 1 FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;"
    query = session.prepare(query_text)
    result = session.transaction(ydb.SerializableReadWrite()).execute(
        query, {"$user_id": user_id, "$firm_id": firm_id}, commit_tx=True
    )
    if not result[0].rows:
        raise AuthError("User is not a member of the specified firm.")
    logging.info(f"User {user_id} is a member of firm {firm_id}. Access granted.")
    return True

def handler(event, context):
    try:
        # Требование №3 (Часть 1): Проверка JWT токена
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '): raise AuthError("Unauthorized: Missing Bearer token")
        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload: raise AuthError("Invalid or expired token")
        
        requesting_user_id = user_payload['user_id']

        data = request_parser.parse_request_body(event)
        action = data.get('action')
        firm_id = data.get('firm_id')
        
        if not all([action, firm_id]):
            raise LogicError("action and firm_id are required.")

        # Требование №3 (Часть 2): Проверка, что фирма существует и юзер - ее сотрудник
        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        firms_pool = ydb.SessionPool(firms_driver)
        firms_pool.retry_operation_sync(lambda s: check_membership(s, requesting_user_id, firm_id))

        # Маршрутизация
        if action == 'GET_UPLOAD_URL':
            filename = data.get('filename')
            filesize = data.get('filesize')
            return handle_get_upload_url(firm_id, filename, filesize)
        
        elif action == 'DELETE':
            file_key = data.get('file_key')
            return handle_delete(firm_id, file_key)
            
        else:
            raise LogicError(f"Invalid action: {action}")

    except AuthError as e:
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except QuotaExceededError as e:
        return {"statusCode": 413, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error processing storage request: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}

```
#### upload.py
```python
import os
import re
import datetime
import json
from utils import storage_utils
from custom_errors import LogicError, QuotaExceededError

# Квота в байтах (100 МБ)
FIRM_QUOTA_BYTES = 100 * 1024 * 1024

def handle_get_upload_url(firm_id: str, filename: str, filesize: int):
    if not all([filename, filesize]):
        raise LogicError("filename and filesize are required for GET_UPLOAD_URL action.")
    
    if not isinstance(filesize, int) or filesize <= 0:
        raise LogicError("filesize must be a positive integer.")

    s3_client = storage_utils.get_s3_client()
    bucket_name = os.environ['STORAGE_BUCKET_NAME']
    
    # Требование №2: Проверка квоты
    folder_prefix = f"{firm_id}/"
    current_size = storage_utils.get_folder_size(s3_client, bucket_name, folder_prefix)
    
    if (current_size + filesize) > FIRM_QUOTA_BYTES:
        raise QuotaExceededError(f"Upload failed: storage quota for firm {firm_id} will be exceeded. Current size: {current_size}, file size: {filesize}, quota: {FIRM_QUOTA_BYTES}")

    # Требование №1: Генерация имени и пути файла
    now = datetime.datetime.utcnow()
    original_basename, extension = os.path.splitext(filename)
    
    # Очистка имени от небезопасных символов для использования в URL/пути
    safe_basename = re.sub(r'[^\w\d-]', '_', original_basename)
    
    # Формируем имя файла по схеме: оригинальное_имя_firm_id.расширение
    final_filename = f"{safe_basename}_{firm_id}{extension}"
    
    # Формируем полный ключ (путь) в хранилище
    file_key = f"{folder_prefix}{now.year}/{now.month:02d}/{final_filename}"

    # Требование №4: Генерация ссылки для прямой загрузки
    upload_url = storage_utils.generate_presigned_upload_url(s3_client, bucket_name, file_key, filename)
    
    if not upload_url:
        raise Exception("Could not generate an upload URL from the storage service.")

    return {
        "statusCode": 200,
        "body": json.dumps({
            "upload_url": upload_url,
            "file_key": file_key 
        })
    }
```
#### delete.py
```python
import os
import json
from utils import storage_utils
from custom_errors import LogicError, AuthError

def handle_delete(firm_id: str, file_key: str):
    if not file_key:
        raise LogicError("file_key is required for DELETE action.")

    # Требование №3 (Часть 3): Проверка, что ключ файла принадлежит фирме пользователя
    if not file_key.startswith(f"{firm_id}/"):
        raise AuthError("Permission denied: you are not allowed to access this file.")

    s3_client = storage_utils.get_s3_client()
    bucket_name = os.environ['STORAGE_BUCKET_NAME']

    if storage_utils.delete_object(s3_client, bucket_name, file_key):
        return {
            "statusCode": 200,
            "body": json.dumps({"message": "File deleted successfully"})
        }
    else:
        raise Exception(f"Failed to delete file with key: {file_key}. It may not exist or there's a permission issue on the bucket side.")
```
#### custom_errors.py
```python
# custom_errors.py

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass
class QuotaExceededError(Exception): pass
```