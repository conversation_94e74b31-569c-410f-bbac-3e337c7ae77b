Идентификатор - d4e4eh8qs34ce11rmgte
Описание - 𐀪 **Физически удалить** сотрудника из конкретной фирмы с проверкой прав и иерархии

Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: Токен администратора или владельца.
	-> Тело запроса:
		- `firm_id`: **(Обязательно)** ID фирмы, в которой происходит действие.
		- `user_id_to_delete`: ID пользователя, которого нужно удалить.
Внутренняя работа:
	1. **Авторизация**: Проверяется JWT токен, извлекается `user_id` администратора.
	2. **Парсинг запроса**: Используется утилита `utils.request_parser` для извлечения `firm_id` и `user_id_to_delete`.
	3. **Валидация**: Проверяется наличие `firm_id` и `user_id_to_delete`.
	4. **Проверка безопасности**: Запрещается удалять самого себя.
	5. **Транзакция в `firms-database`**:
		- Выполняется один запрос к БД, который находит и администратора, и цель в таблице `Users` по указанному `firm_id`.
		- **Проверка иерархии**: Роль администратора должна быть строго выше роли удаляемого сотрудника (OWNER > ADMIN > EMPLOYEE).
		- **Проверка на владельца**: Запрещается удалять пользователя с ролью `OWNER`.
	6. **Удаление**: Выполняется `DELETE FROM Users`, физически удаляя запись, соответствующую **и `user_id`, и `firm_id`**.
На выходе:
	-> `200 OK`: {"message": "Employee successfully deleted."}
	-> `400 Bad Request`: Отсутствуют обязательные поля или попытка удалить самого себя.
	-> `403 Forbidden`: Недостаточно прав или нарушение иерархии ролей.
	-> `404 Not Found`: Администратор или сотрудник не найдены в указанной фирме.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py`
- **Переменные окружения**:
	- `YDB_ENDPOINT_FIRMS`, `YDB_DATABASE_FIRMS`
	- `SA_KEY_FILE`
	- `JWT_SECRET`

---

```python
import json
import os
import logging
import ydb
from utils import auth_utils, ydb_utils, request_parser

logging.basicConfig(level=logging.INFO)

class AuthError(Exception): pass
class LogicError(Exception): pass
class NotFoundError(Exception): pass

# Словарь для определения веса ролей
ROLE_HIERARCHY = {
    "OWNER": 3,
    "ADMIN": 2,
    "EMPLOYEE": 1
}

def get_highest_role_score(roles_json_str: str) -> int:
    """Возвращает наивысший балл из списка ролей пользователя."""
    roles = json.loads(roles_json_str)
    if not roles:
        return 0
    return max(ROLE_HIERARCHY.get(role, 0) for role in roles)

def handler(event, context):
    try:
        # 1. Авторизация администратора
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized")
        
        token = auth_header.split(' ')[1]
        admin_payload = auth_utils.verify_jwt(token)
        if not admin_payload or 'user_id' not in admin_payload:
            raise AuthError("Invalid token")
        
        admin_user_id = admin_payload['user_id']

        # 2. Валидация входных данных
        try:
            data = request_parser.parse_request_body(event)
        except ValueError as e:
            raise LogicError(str(e))

        user_id_to_delete = data.get('user_id_to_delete')
        firm_id = data.get('firm_id')

        if not all([user_id_to_delete, firm_id]):
            raise LogicError("firm_id and user_id_to_delete are required.")

        # 3. Проверка на удаление самого себя
        if admin_user_id == user_id_to_delete:
            raise LogicError("You cannot delete yourself.")

        driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        pool = ydb.SessionPool(driver)

        def delete_employee_transaction(session):
            tx = session.transaction(ydb.SerializableReadWrite())

            # 4. Один запрос для получения данных админа и цели в рамках ОДНОЙ фирмы
            query = session.prepare("""
                DECLARE $admin_id AS Utf8;
                DECLARE $target_id AS Utf8;
                DECLARE $firm_id AS Utf8;
                SELECT user_id, roles FROM Users 
                WHERE firm_id = $firm_id AND user_id IN ($admin_id, $target_id);
            """)
            res = tx.execute(query, {
                '$admin_id': admin_user_id, 
                '$target_id': user_id_to_delete,
                '$firm_id': firm_id
            })
            
            admin_data, target_data = None, None
            for row in res[0].rows:
                if row.user_id == admin_user_id:
                    admin_data = row
                elif row.user_id == user_id_to_delete:
                    target_data = row
            
            if not admin_data:
                raise NotFoundError("Requesting user (admin) not found in the specified firm.")
            if not target_data:
                raise NotFoundError("Target user to delete not found in the specified firm.")

            # 5. Проверка безопасности и иерархии
            target_roles = json.loads(target_data.roles)
            if "OWNER" in target_roles:
                raise AuthError("Cannot delete the firm owner.")

            admin_score = get_highest_role_score(admin_data.roles)
            target_score = get_highest_role_score(target_data.roles)

            if admin_score <= target_score:
                raise AuthError("Insufficient permissions: your role must be higher than the target user's role.")

            # 6. Физическое удаление пользователя из КОНКРЕТНОЙ фирмы
            delete_query = session.prepare("""
                DECLARE $user_id AS Utf8;
                DECLARE $firm_id AS Utf8;
                DELETE FROM Users WHERE user_id = $user_id AND firm_id = $firm_id;
            """)
            tx.execute(
                delete_query,
                {'$user_id': user_id_to_delete, '$firm_id': firm_id}
            )
            tx.commit()
            return True

        pool.retry_operation_sync(delete_employee_transaction)
        return {"statusCode": 200, "body": json.dumps({"message": "Employee successfully deleted."})}

    except AuthError as e:
        return {"statusCode": 403, "body": json.dumps({"message": str(e)})}
    except LogicError as e:
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Error deleting employee: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```

---

```
{
  "httpMethod": "POST",
  "headers": {
    "Content-Type": "application/json",
    "User-Agent": "Yandex-Cloud-Function-Test",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.q0nvV5-Nst5Zim-1IR4C0G16eYZEGdnfIAY6a1kf2qs"
  },
  "multiValueHeaders": {
    "Content-Type": [
      "application/json"
    ],
    "User-Agent": [
      "Yandex-Cloud-Function-Test"
    ],
    "Authorization": [
      "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.q0nvV5-Nst5Zim-1IR4C0G16eYZEGdnfIAY6a1kf2qs"
    ]
  },
  "requestContext": {
    "identity": {
      "sourceIp": "127.0.0.1",
      "userAgent": "Yandex-Cloud-Function-Test"
    },
    "httpMethod": "POST",
    "requestId": "test-request-id-delete-employee-with-firm-id",
    "requestTimeEpoch": 1672522560
  },
  "body": "{\"firm_id\": \"f7933a40-bcd0-42b4-ad6c-bfc4bbb8bc6e\", \"user_id_to_delete\": \"a7e7d7c9-10dd-46da-910a-20d0c320ccf3\"}",
  "isBase64Encoded": false
}
```