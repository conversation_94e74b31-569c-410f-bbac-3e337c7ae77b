import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:pdfx/pdfx.dart';

import '../../../cubit/tariffs_and_storage_cubit.dart';
import '../../../cubit/tariffs_and_storage_state.dart';
import '../../../../../employees_and_firms/presentation/cubit/active_firm_cubit.dart';

class PdfPreviewWidget extends StatefulWidget {
  final String fileKey;
  final String fileName;

  const PdfPreviewWidget({
    super.key,
    required this.fileKey,
    required this.fileName,
  });

  @override
  State<PdfPreviewWidget> createState() => _PdfPreviewWidgetState();
}

class _PdfPreviewWidgetState extends State<PdfPreviewWidget> {
  PdfController? _pdfController;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPdf();
  }

  @override
  void dispose() {
    _pdfController?.dispose();
    super.dispose();
  }

  Future<void> _loadPdf() async {
    try {
      final downloadUrl = await _getDownloadUrl();
      if (downloadUrl == null) {
        setState(() {
          _errorMessage = 'Не удалось получить ссылку для предпросмотра';
          _isLoading = false;
        });
        return;
      }

      final pdfController = PdfController(
        document: PdfDocument.openData(
          http
              .get(Uri.parse(downloadUrl))
              .then((response) => response.bodyBytes),
        ),
      );

      setState(() {
        _pdfController = pdfController;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Ошибка загрузки PDF: $e';
        _isLoading = false;
      });
    }
  }

  Future<String?> _getDownloadUrl() async {
    final storageCubit = context.read<TariffsAndStorageCubit>();
    final firmState = context.read<ActiveFirmCubit>().state;

    if (firmState.selectedFirm == null) {
      return null;
    }

    final firmId = firmState.selectedFirm!.id;
    final completer = Completer<String?>();
    late StreamSubscription sub;

    sub = storageCubit.stream.listen((state) {
      if (state is FileDownloadUrlReady && state.fileKey == widget.fileKey) {
        if (!completer.isCompleted) {
          completer.complete(state.downloadUrl);
        }
        sub.cancel();
      } else if (state is TariffsAndStorageError) {
        if (!completer.isCompleted) {
          completer.complete(null);
        }
        sub.cancel();
      }
    });

    storageCubit.getDownloadUrl(firmId: firmId, fileKey: widget.fileKey);

    // Таймаут безопасности
    Future.delayed(const Duration(seconds: 15), () {
      if (!completer.isCompleted) {
        completer.complete(null);
        sub.cancel();
      }
    });

    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(color: Theme.of(context).colorScheme.error),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_pdfController == null) {
      return const Center(child: Text('Не удалось загрузить PDF'));
    }

    return PdfView(controller: _pdfController!);
  }
}
