Идентификатор - d4eu095gn2tga52no22p
Описание - Подтвердить регистрацию с помощью кода

Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `email`: Email пользователя.
	-> `code`: 6-значный код из email.
Внутренняя работа:
	-> Находит в YDB неактивного пользователя по `email`.
	-> Сравнивает код и проверяет срок его действия.
	-> Активирует пользователя (`is_active = true`), удаляет код.
	-> Генерирует и возвращает JWT.
На выходе:
	-> `200 OK`: {"token": "<jwt_token>"}
	-> `400 Bad Request`: {"message": "Invalid or expired code."}

---
#### Зависимости и окружение
- **Необходимые утилиты**: utils/auth_utils.py, utils/ydb_utils.py
- **Переменные окружения**:
    - YDB_ENDPOINT - Эндпоинт базы данных (Источник: [[🗃️ Структура YDB]])
    - YDB_DATABASE - Путь к базе данных (Источник: [[🗃️ Структура YDB]])
    - SA_KEY_FILE - ydb_sa_key.json (Источник: [[ydb_sa_key.json]])
    - JWT_SECRET - Надежная секретная строка (Генерируется пользователем)

---

```python
import json, os, datetime, pytz, logging
from utils import ydb_utils, auth_utils

logging.basicConfig(level=logging.INFO)

def handler(event, context):
    body = json.loads(event.get('body', '{}'))
    email = body.get('email')
    code = body.get('code')

    if not all([email, code]):
        return {"statusCode": 400, "body": json.dumps({"message": "Email and code are required."})}

    driver = ydb_utils.get_ydb_driver()
    pool = ydb.SessionPool(driver)

    def transaction(session):
        # Ищем неактивного пользователя
        result_sets = session.transaction(ydb.SerializableReadWrite()).execute(
            f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8;
            SELECT user_id, verification_code, code_expires_at FROM users WHERE email = $email AND is_active = false;
            """,
            {'$email': email}, commit_tx=True
        )
        if not result_sets[0].rows:
            return {"status": 400, "message": "User not found or already active."}

        user_data = result_sets[0].rows[0]
        # Проверяем код и срок его жизни
        if user_data.verification_code != code or datetime.datetime.now(pytz.utc) > user_data.code_expires_at:
            return {"status": 400, "message": "Invalid or expired code."}

        # Активируем пользователя
        user_id = user_data.user_id
        session.transaction(ydb.SerializableReadWrite()).execute(
            f"""
            PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $user_id AS Utf8;
            UPDATE users SET is_active = true, verification_code = NULL, code_expires_at = NULL WHERE user_id = $user_id;
            """,
            {'$user_id': user_id}, commit_tx=True
        )
        token = auth_utils.generate_jwt(user_id, email)
        return {"status": 200, "token": token}

    try:
        result = pool.retry_operation_sync(transaction)
        if result["status"] == 200:
            return {"statusCode": 200, "body": json.dumps({"token": result["token"]})}
        else:
            return {"statusCode": result["status"], "body": json.dumps({"message": result["message"]})}
    except Exception as e:
        logging.error(f"Error during registration confirmation: {e}")
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```