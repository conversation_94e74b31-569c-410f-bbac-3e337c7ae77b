import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';

import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';

import 'package:balansoved_enterprise/features/employees_and_firms/presentation/cubit/active_firm_cubit.dart';
import 'package:balansoved_enterprise/features/clients/presentation/cubit/clients_cubit.dart';

import 'package:balansoved_enterprise/features/calendar/presentation/cubit/calendar_cubit.dart';
import 'package:balansoved_enterprise/features/calendar/presentation/cubit/calendar_state.dart';
import 'package:balansoved_enterprise/features/calendar/presentation/widgets/calendar_table.dart';
import 'package:balansoved_enterprise/features/calendar/presentation/widgets/calendar_navigation.dart';
import 'package:balansoved_enterprise/router.dart';
import 'package:balansoved_enterprise/presentation/widgets/smart_date_picker_dialog.dart';

@RoutePage()
class CalendarPage extends StatefulWidget {
  const CalendarPage({
    super.key,
    @PathParam('year') required this.initialYear,
    @PathParam('month') required this.initialMonth,
    @QueryParam('scrollOffset') this.initialScrollOffset,
  });

  final int initialMonth;
  final int initialYear;
  final double? initialScrollOffset;

  @override
  State<CalendarPage> createState() => _CalendarPageState();
}

class _CalendarPageState extends State<CalendarPage> {
  late DateTime _selectedDate;

  // Контроллер вертикального скролла таблицы
  final ScrollController _verticalScrollController = ScrollController();

  // Оффсет прокрутки, который получили из URL и ещё не применили
  double? _pendingInitialScrollOffset;

  // Последний сохранённый оффсет, чтобы не писать одинаковые значения
  double _lastSavedOffset = 0;

  // Debounce-таймер для уменьшения числа записей в history
  Timer? _scrollDebounce;

  // Флаг: программная прокрутка в процессе (не реагировать на _onScroll)
  bool _isApplyingInitialScroll = false;

  @override
  void initState() {
    super.initState();

    // Устанавливаем дату из переданных параметров
    _selectedDate = DateTime(widget.initialYear, widget.initialMonth, 1);

    // Сохраняем scrollOffset, но применим только когда таблица будет готова
    _pendingInitialScrollOffset = widget.initialScrollOffset;

    // Слушатель скролла для обновления URL
    _verticalScrollController.addListener(_onScroll);

    // Загружаем задачи за текущий месяц при инициализации
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
      if (firm != null) {
        // Загружаем клиентов, если их нет
        final clientsState = context.read<ClientsCubit>().state;
        if (clientsState.clients.isEmpty && !clientsState.isLoading) {
          context.read<ClientsCubit>().fetchClients(firm.id);
        }

        context.read<CalendarCubit>().fetchDatedTasks(
          firm.id,
          month: _selectedDate.month,
          year: _selectedDate.year,
        );
      }
    });
  }

  @override
  void dispose() {
    _verticalScrollController.removeListener(_onScroll);
    _scrollDebounce?.cancel();
    _verticalScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Пытаемся применить исходный scrollOffset после каждой отрисовки кадра,
    // пока он ещё не был применён.
    if (_pendingInitialScrollOffset != null) {
      WidgetsBinding.instance.addPostFrameCallback(
        (_) => _tryApplyInitialScroll(),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Заголовок
          Text(
            'Календарь клиентов',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 16),

          // Основная таблица календаря
          Expanded(
            child: BlocBuilder<CalendarCubit, CalendarState>(
              builder: (context, calendarState) {
                // Если календарь еще не инициализирован, запускаем загрузку
                if (calendarState is CalendarInitial) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    final firm =
                        context.read<ActiveFirmCubit>().state.selectedFirm;
                    if (firm != null) {
                      context.read<CalendarCubit>().fetchDatedTasks(
                        firm.id,
                        month: _selectedDate.month,
                        year: _selectedDate.year,
                      );
                    }
                  });
                }

                return BlocBuilder<ClientsCubit, ClientsState>(
                  builder: (context, clientsState) {
                    // Если клиенты еще не загружены, запускаем их загрузку
                    if (clientsState.clients.isEmpty &&
                        !clientsState.isLoading) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        final firm =
                            context.read<ActiveFirmCubit>().state.selectedFirm;
                        if (firm != null) {
                          context.read<ClientsCubit>().fetchClients(firm.id);
                        }
                      });
                    }

                    if (calendarState is CalendarLoading ||
                        clientsState.isLoading) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (calendarState is CalendarError) {
                      return Center(
                        child: Text('Ошибка: ${calendarState.message}'),
                      );
                    }

                    if (calendarState is CalendarLoaded ||
                        calendarState is CalendarInitial) {
                      final tasks =
                          calendarState is CalendarLoaded
                              ? calendarState.tasks
                              : <TaskEntity>[];
                      return CalendarTable(
                        tasks: tasks,
                        clients: clientsState.clients,
                        selectedDate: _selectedDate,
                        scrollController: _verticalScrollController,
                      );
                    }

                    return const Center(
                      child: Text('Нет данных для отображения'),
                    );
                  },
                );
              },
            ),
          ),

          // Нижняя панель с переключением месяцев
          CalendarNavigation(
            selectedDate: _selectedDate,
            onPreviousMonth: _moveToPreviousMonth,
            onNextMonth: _moveToNextMonth,
            onCurrentMonth: () {
              setState(() {
                _selectedDate = DateTime.now();
              });
              _loadTasksForCurrentMonth();
            },
            onSelectMonth: _selectMonth,
          ),
        ],
      ),
    );
  }

  void _moveToPreviousMonth() {
    final newDate = DateTime(_selectedDate.year, _selectedDate.month - 1, 1);
    _updateDateAndUrl(newDate);
  }

  void _moveToNextMonth() {
    final newDate = DateTime(_selectedDate.year, _selectedDate.month + 1, 1);
    _updateDateAndUrl(newDate);
  }

  void _updateDateAndUrl(DateTime newDate) {
    setState(() {
      _selectedDate = newDate;
    });

    // Обновляем URL с новой датой и текущей позицией прокрутки
    final currentOffset =
        _verticalScrollController.hasClients
            ? _verticalScrollController.offset
            : 0.0;

    context.router.navigate(
      CalendarRoute(
        initialMonth: newDate.month,
        initialYear: newDate.year,
        initialScrollOffset: currentOffset,
      ),
    );

    _loadTasksForCurrentMonth();
  }

  Future<void> _selectMonth() async {
    final pickedDate = await SmartDatePickerDialog.show(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      helpText: 'Выберите месяц и год',
      allowClear: false,
    );

    if (pickedDate != null) {
      final newDate = DateTime(pickedDate.year, pickedDate.month, 1);
      _updateDateAndUrl(newDate);
    }
  }

  void _loadTasksForCurrentMonth() {
    final firm = context.read<ActiveFirmCubit>().state.selectedFirm;
    if (firm != null) {
      context.read<CalendarCubit>().fetchDatedTasks(
        firm.id,
        month: _selectedDate.month,
        year: _selectedDate.year,
      );
    }
  }

  /* ===================== Scroll helpers ===================== */

  void _tryApplyInitialScroll() {
    if (_pendingInitialScrollOffset == null) return;
    if (!_verticalScrollController.hasClients) {
      _scheduleNextAttempt();
      return;
    }

    final max = _verticalScrollController.position.maxScrollExtent;
    final desired = _pendingInitialScrollOffset!;

    // Если содержимое уже достаточно большое – прокручиваем.
    if (max >= desired) {
      // Отключаем слушатель, чтобы _onScroll не сработал от программного jumpTo
      _isApplyingInitialScroll = true;
      _verticalScrollController.jumpTo(desired);
      _lastSavedOffset =
          desired; // синхронизируем, чтобы избежать немедленной перезаписи
      _pendingInitialScrollOffset = null; // применили

      // Включаем слушатель обратно через микрозадачу
      Future.microtask(() {
        _isApplyingInitialScroll = false;
      });
    } else {
      // содержимое ещё не доросло – пробуем снова на следующем кадре
      _scheduleNextAttempt();
    }
  }

  void _scheduleNextAttempt() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) _tryApplyInitialScroll();
    });
  }

  void _onScroll() {
    // Игнорируем события скролла во время программного применения offset
    if (_isApplyingInitialScroll) return;
    if (!_verticalScrollController.hasClients) return;

    // Debounce, чтобы не дергать history слишком часто
    _scrollDebounce?.cancel();
    _scrollDebounce = Timer(const Duration(milliseconds: 300), () {
      if (_isApplyingInitialScroll) return; // двойная проверка

      final offset = _verticalScrollController.offset;

      // Избегаем лишних записей, если позиция не изменилась заметно
      if ((offset - _lastSavedOffset).abs() < 5) return;
      _lastSavedOffset = offset;

      // Обновляем URL через pushReplacement, чтобы избежать полной перерисовки
      if (mounted) {
        // Обновляем URL, заменяя текущий маршрут
        context.router.replace(
          CalendarRoute(
            initialMonth: _selectedDate.month,
            initialYear: _selectedDate.year,
            initialScrollOffset: offset,
          ),
        );
      }
    });
  }
}
