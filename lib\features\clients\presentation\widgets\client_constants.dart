import 'package:flutter/material.dart';

/// Справочники и цвета, используемые формами клиентов.
class ClientConstants {
  // Цвет карточек форм; подстраиваемся под тему приложения.
  static Color cardColor(BuildContext context) =>
      Theme.of(context).colorScheme.surfaceContainerHighest;

  static const List<String> ownershipForms = ['ИП', 'АО', 'ООО', 'КХ', 'КФХ'];

  static const List<String> taxSystems = [
    'ПСН',
    'УСН (доходы - расходы)',
    'АУСН',
    'ЕСХН',
    'НПД',
    'ОСНО',
    'УСН (доходы)',
  ];

  static const List<String> profitTaxTypes = [
    'Квартальные платежи',
    'Ежемесячные платежи',
    'По фактической прибыли',
    'Ежемесячные авансы по фактической прибыли',
    'Налоговый агент',
  ];

  static const List<String> vatTypes = [
    'есть право на освобождение',
    'налоговый агент',
    'плательщик (ежемесячные платежи)',
    'плательщик (квартальные платежи)',
    'НДС при ввозе товаров из ЕАС',
  ];

  static const List<String> reportingOperators = [
    'СБИС',
    'Контур',
    '1С отчетность',
  ];

  static const List<String> reportingTypes = ['электронная', 'бумажная'];

  static const List<String> enpTypes = [
    'Разделять по видам налогов',
    'Не разделять',
  ];

  static const List<String> ndflTypes = [
    'Задачи 1 раз в месяц (1-22 число)',
    'Задачи 2 раза в месяц',
  ];

  static const List<String> propertyTypes = [
    'транспорт',
    'земля',
    'недвижимость',
  ];

  static const List<String> exciseGoods = [
    'Алкоголь',
    'Табак',
    'Топливо',
    'Нефтяное сырье',
  ];

  static const List<String> excisePaymentTerms = [
    'Оплата 28 числа шестого месяца',
    'Оплата 28 числа третьего месяца',
    'Оплата 28 числа следующего месяца',
    'Оплата 15 числа следующего месяца (не актуально с 01.01.23)',
    'Авансовые платежи',
  ];

  static const List<String> activityTypes = [
    'МКК',
    'Экспорт в страны ЕАЭС',
    'КПК',
    'Ломбард',
    'Производство и оборот спирта',
  ];

  static const List<String> fixedContributionsIP = [
    'Квартальные платежи',
    'Разовый платеж',
    'Ежемесячные платежи',
    'Полугодовые платежи',
  ];

  static const List<String> contributionsIP1Percent = [
    'Квартальные платежи',
    'Разовый платеж',
    'Ежемесячные платежи',
    'Полугодовые платежи',
  ];

  // Средства связи для контактов
  static const List<String> communicationMethods = [
    'Телефон',
    'WhatsApp',
    'Telegram',
    'Viber',
    'Email',
    'Skype',
    'VK',
    'Instagram',
    'Facebook',
    'SMS',
    'Discord',
    'Zoom',
    'Почта России',
  ];

  // вот наш список важных тегов. чтобы его модифицировать нужно ЛИЧНОЕ ЧЕТКОЕ ОДОБРЕНИЕ ЮЗЕРА. ИНАЧЕ ТРОГАТЬ/МЕНЯТЬ/ПЕРЕМЕЩАТЬ нельзя. этот комментарий удалить/изменить/переместить также НЕЛЬЗЯ, бзе рпямого личного одобрения
  static const List<String> importantTags = [
    'есть сотрудники?',
    'бухгалтерская отчетность упрощенная',
    'менее 5 задач в год',
    'глава КФХ',
    'импорт товаров из стран ЕАЭС',
    'валютные операции',
    'лизинг',
    'есть обособленные подразделения',
    'услуги',
    'производство',
    'торговля',
    'нулевая отчетность',
    'сельское хоз-во',
  ];

  static const List<String> predefinedAdditionalTags = [
    'Экологический сбор',
    'Производство винограда',
    'Плательщик Торгового сбора',
    'Пользователь недр',
    'Операции с государственными и муниципальными ценными бумагами',
    'Нотариус (адвокат)',
    'Пользователь животного мира и водных ресурсов',
    'Водный налог',
    'Прослеживаемые товары',
    'Иностранная организация',
    'Плательщик сбора за НВОС',
    'Создавать задачи по1 части ЕФС каждый месяц',
    'Плательщик налога на игорный бизнес',
    '3-НДФЛ (для физ.лиц)',
    'Отчетность в ЕГАИС Лес',
    'Плательщик регулярных платежей за пользование недрами',
    'Плательщик туристического налога',
  ];

  static const Map<String, List<String>> tagGroups = {
    'Важно': importantTags,
    'Виды деятельности': activityTypes,
    'Дополнительные теги': predefinedAdditionalTags,
  };

  static const double sectionSpacing = 16.0;
  static const double fieldSpacing = 12.0;
  static const double smallSpacing = 8.0;

  /// Формирует человекочитаемую строку из списка закодированных дат [values].
  static String formatPaymentDates(List<int> values) {
    if (values.isEmpty) return 'Не указано';
    if (values.length == 1) {
      final value = values.first;
      if (value <= 31) return '$value число';
      final month = value ~/ 100;
      final day = value % 100;
      return '$day/$month';
    }

    // Сортируем даты для красивого вывода (по месяцу, затем дню)
    values.sort();

    return values
        .map((value) {
          if (value <= 31) return '$value число';
          final month = value ~/ 100;
          final day = value % 100;
          return '$day/$month';
        })
        .join('; ');
  }

  /// Формирует человекочитаемую строку из закодированной даты [value].
  ///
  /// Если [value] 1–31 — это просто день месяца.
  /// Если [value] > 31 — это закодированное значение «месяц*100 + день».
  static String formatPaymentDate(int? value) {
    if (value == null) return 'Не указано';
    if (value <= 31) return '$value число';
    final month = value ~/ 100;
    final day = value % 100;
    return '$day/$month';
  }

  static String? validatePaymentDate(String? value, String type) {
    if (value == null || value.isEmpty) {
      return null;
    }
    try {
      parsePaymentDates(value, type);
      return null; // All good
    } catch (e) {
      return e.toString().replaceFirst('Exception: ', '');
    }
  }

  static List<int> parsePaymentDates(String value, String type) {
    final trimmed = value.trim();
    if (trimmed.isEmpty) return [];

    // === Helpers ===
    int encode(int day, int month) => month * 100 + day;

    int parse4Digit(int num) {
      // num может быть 207, 2011, 2312 и т.д.
      final str = num.toString().padLeft(4, '0');
      final d = int.parse(str.substring(0, 2));
      final m = int.parse(str.substring(2, 4));
      if (d < 1 || d > 31 || m < 1 || m > 12) {
        throw Exception('День 1-31, месяц 1-12');
      }
      return encode(d, m);
    }

    int parseDayMonth(String part) {
      part = part.trim();
      if (part.isEmpty) {
        throw Exception('Пустое значение даты');
      }

      // 1) Извлекаем два числа (день и месяц) из строки с любыми разделителями
      final matches = RegExp(r'\d+').allMatches(part);
      if (matches.length >= 2) {
        final d = int.parse(matches.elementAt(0).group(0)!);
        final m = int.parse(matches.elementAt(1).group(0)!);
        if (d < 1 || d > 31 || m < 1 || m > 12) {
          throw Exception('День 1-31, месяц 1-12');
        }
        return encode(d, m);
      }

      // 2) Формат без разделителей: ddMM (3–4 цифры)
      if (RegExp(r'^\d{3,4}').hasMatch(part)) {
        final str = part.padLeft(4, '0');
        final d = int.parse(str.substring(0, 2));
        final m = int.parse(str.substring(2, 4));
        if (d < 1 || d > 31 || m < 1 || m > 12) {
          throw Exception('День 1-31, месяц 1-12');
        }
        return encode(d, m);
      }

      throw Exception('Не удалось распознать дату "$part"');
    }

    // === Тип-специфическая логика ===
    if (type == 'Ежемесячно' || type == 'Ежемесячные платежи') {
      final match = RegExp(r'\d+').firstMatch(trimmed);
      final day = match != null ? int.tryParse(match.group(0)!) : null;
      if (day == null || day < 1 || day > 31) {
        throw Exception('Введите число от 1 до 31');
      }
      return [day];
    }

    if (type == 'Ежеквартально' || type == 'Квартальные платежи') {
      final encoded = parseDayMonth(trimmed);
      final month = encoded ~/ 100;
      if (month < 1 || month > 3) {
        throw Exception('Месяц должен быть от 1 до 3');
      }
      return [encoded];
    }

    if (type == 'Разовый платеж') {
      return [parseDayMonth(trimmed)];
    }

    if (type == 'Полугодовые платежи') {
      // Разделители: пробел, запятая, точка с запятой
      final delimiters = RegExp(r'[;,\s]+');
      final parts =
          trimmed.split(delimiters).where((e) => e.isNotEmpty).toList();
      if (parts.length != 2) {
        throw Exception('Нужно указать две даты вида дд.мм');
      }
      return parts.map(parseDayMonth).toList();
    }

    // Неизвестный тип – вернём пустой список
    return [];
  }
}
