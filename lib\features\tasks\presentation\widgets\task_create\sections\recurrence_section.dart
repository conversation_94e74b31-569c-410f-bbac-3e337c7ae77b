import 'package:flutter/material.dart';

// ------------------ Повторение задачи ------------------
class RecurrenceSection extends StatelessWidget {
  final String recurrenceType;
  final String recurrenceInterval;
  final Function(String) onRecurrenceTypeChanged;
  final Function(String) onRecurrenceIntervalChanged;

  const RecurrenceSection({
    super.key,
    required this.recurrenceType,
    required this.recurrenceInterval,
    required this.onRecurrenceTypeChanged,
    required this.onRecurrenceIntervalChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Повторение задачи',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        DropdownButtonFormField<String>(
          value: recurrenceType,
          decoration: const InputDecoration(
            labelText: 'Тип повторения',
            border: OutlineInputBorder(),
          ),
          items: const [
            DropdownMenuItem(value: 'none', child: Text('Не повторять')),
            DropdownMenuItem(value: 'periodic', child: Text('Периодично')),
            DropdownMenuItem(value: 'arbitrary', child: Text('Произвольно')),
            DropdownMenuItem(value: 'еженедельно', child: Text('Еженедельно')),
          ],
          onChanged: (value) => onRecurrenceTypeChanged(value ?? 'none'),
        ),
        if (recurrenceType == 'periodic') ...[
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: recurrenceInterval,
            decoration: const InputDecoration(
              labelText: 'Интервал',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'monthly', child: Text('Ежемесячно')),
              DropdownMenuItem(
                value: 'quarterly',
                child: Text('Ежеквартально'),
              ),
              DropdownMenuItem(value: 'yearly', child: Text('Ежегодно')),
            ],
            onChanged:
                (value) => onRecurrenceIntervalChanged(value ?? 'monthly'),
          ),
        ],
      ],
    );
  }
}
