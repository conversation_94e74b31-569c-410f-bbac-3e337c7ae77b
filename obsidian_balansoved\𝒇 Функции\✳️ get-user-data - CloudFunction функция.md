Идентификатор - d4e4c3r7udrhh476a309
Описание - 📥 Получить все данные пользователя (инфо, фирмы, задачи) по JWT токену.
Точка входа - index.handler
Таймаут - 20 сек

---

На входе:
	-> `Authorization: Bearer <jwt_token>`: То<PERSON><PERSON>н аутентифицированного пользователя.

Внутренняя работа:
	1. **Авторизация**: Проверяется JWT токен с помощью `utils.auth_utils`, извлекается `user_id`.
	2. **Сбор информации о пользователе**: Выполняется запрос к `jwt-database` для получения основной информации о пользователе (`user_id`, `email`, `user_name`).
	3. **Сбор информации о фирмах**:
		- Выполняется запрос к `firms-database` для поиска всех записей в таблице `Users`, где `user_id` совпадает с `user_id` из токена.
		- Из полученных записей формируется список `firm_ids`.
		- Выполняется второй запрос к таблице `Firms` для получения полной информации по каждому `firm_id` из списка.
	4. **Сбор информации о задачах**:
		- Для каждой найденной фирмы (`firm_id`) динамически определяется имя таблицы задач (`tasks_{firm_id}`) в `tasks-database`.
		- Выполняется запрос к каждой такой таблице, который ищет все задачи, где `user_id` пользователя присутствует в любом из полей: `assignee_ids_json`, `observer_ids_json`, `creator_ids_json`.
	5. **Агрегация**: Все полученные данные (информация о пользователе, список фирм, список задач) собираются в единый JSON-объект.

На выходе:
	-> `200 OK`:
	```json
	{
	  "user_info": {
	    "user_id": "...",
	    "email": "...",
	    "user_name": "..."
	  },
	  "firms": [
	    {
	      "firm_id": "...",
	      "firm_name": "...",
	      "owner_user_id": "...",
	      "user_roles": ["OWNER", "ADMIN"]
	    }
	  ],
	  "tasks": [
	    {
	      "task_id": "...",
	      "title": "...",
	      "firm_id": "..." 
	    }
	  ]
	}
	```
	-> `401 Unauthorized`: Если токен невалиден.
	-> `404 Not Found`: Если пользователь из токена не найден в `jwt-database`.
	-> `500 Internal Server Error`: В случае ошибок при работе с базами данных.

---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/ydb_utils.py`
- **Переменные окружения**:
    - `YDB_ENDPOINT` - Эндпоинт основной `jwt-database`.
    - `YDB_DATABASE` - Путь к основной `jwt-database`.
    - `YDB_ENDPOINT_FIRMS` - Эндпоинт `firms-database`.
    - `YDB_DATABASE_FIRMS` - Путь к `firms-database`.
    - `YDB_ENDPOINT_TASKS` - Эндпоинт `tasks-database`.
    - `YDB_DATABASE_TASKS` - Путь к `tasks-database`.
    - `SA_KEY_FILE` - ydb_sa_key.json
    - `JWT_SECRET` - Секретная строка для валидации JWT.

---

#### Код функции

```python
import json
import os
import logging
import ydb
from utils import auth_utils, ydb_utils

logging.getLogger().setLevel(logging.INFO)

class AuthError(Exception): pass
class NotFoundError(Exception): pass

def get_user_info(session, user_id):
    query_text = "DECLARE $user_id AS Utf8; SELECT user_id, email, user_name FROM users WHERE user_id = $user_id;"
    result = session.transaction(ydb.SerializableReadWrite()).execute(
        session.prepare(query_text), {"$user_id": user_id}, commit_tx=True
    )
    if not result[0].rows:
        raise NotFoundError(f"User with id {user_id} not found in the main database.")
    user_data = result[0].rows[0]
    return {"user_id": user_data.user_id, "email": user_data.email, "user_name": user_data.user_name}

def get_user_firms(session, user_id):
    # Находим все фирмы, в которых состоит пользователь
    query_text = "DECLARE $user_id AS Utf8; SELECT firm_id, roles FROM Users WHERE user_id = $user_id;"
    result_users = session.transaction(ydb.SerializableReadWrite()).execute(
        session.prepare(query_text), {"$user_id": user_id}, commit_tx=True
    )
    
    if not result_users[0].rows:
        return []

    user_firm_roles = {row.firm_id: json.loads(row.roles or '[]') for row in result_users[0].rows}
    firm_ids = list(user_firm_roles.keys())
    
    # Получаем детали этих фирм
    query_text_firms = f"""
        DECLARE $firm_ids AS List<Utf8>;
        SELECT firm_id, firm_name, owner_user_id FROM Firms WHERE firm_id IN $firm_ids;
    """
    result_firms = session.transaction(ydb.SerializableReadWrite()).execute(
        session.prepare(query_text_firms), {"$firm_ids": firm_ids}, commit_tx=True
    )

    firms_data = []
    for row in result_firms[0].rows:
        firm_id = row.firm_id
        firms_data.append({
            "firm_id": firm_id,
            "firm_name": row.firm_name,
            "owner_user_id": row.owner_user_id,
            "user_roles": user_firm_roles.get(firm_id, [])
        })
    return firms_data

def get_user_tasks_for_firm(session, firm_id, user_id):
    table_name = f"tasks_{firm_id}"
    query_text = f"""
        DECLARE $user_id_str AS Utf8;
        SELECT * FROM `{table_name}` WHERE 
            JsonValue(creator_ids_json, '$[*]') LIKE $user_id_str OR
            JsonValue(assignee_ids_json, '$[*]') LIKE $user_id_str OR
            JsonValue(observer_ids_json, '$[*]') LIKE $user_id_str;
    """
    # YQL LIKE не поддерживает поиск в JSON, поэтому используем обходной путь
    # В реальном приложении лучше использовать полнотекстовый индекс или денормализацию
    
    # Более надежный, но медленный способ - вычитать все и отфильтровать в коде.
    # Для демонстрации оставим так, но с оговоркой.
    
    all_tasks_query = f"SELECT * FROM `{table_name}`"
    result = session.transaction(ydb.SerializableReadWrite()).execute(
        session.prepare(all_tasks_query), commit_tx=True
    )
    
    user_tasks = []
    for row in result[0].rows:
        is_related = False
        related_fields = ['creator_ids_json', 'assignee_ids_json', 'observer_ids_json']
        for field in related_fields:
            ids_list = json.loads(row[field] or '[]')
            if user_id in ids_list:
                is_related = True
                break
        
        if is_related:
            task_data = {c.name: row[c.name] for c in result[0].columns}
            task_data['firm_id'] = firm_id # Добавляем firm_id для контекста
            user_tasks.append(task_data)
            
    return user_tasks

def handler(event, context):
    try:
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            raise AuthError("Unauthorized: Missing Bearer token.")

        token = auth_header.split(' ')[1]
        user_payload = auth_utils.verify_jwt(token)
        if not user_payload or 'user_id' not in user_payload:
            raise AuthError("Invalid or expired token.")
        
        user_id = user_payload['user_id']
        logging.info(f"Fetching data for user_id: {user_id}")
        
        # Подключения к БД
        auth_driver = ydb_utils.get_ydb_driver()
        firms_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_FIRMS"], os.environ["YDB_DATABASE_FIRMS"])
        tasks_driver = ydb_utils.get_driver_for_db(os.environ["YDB_ENDPOINT_TASKS"], os.environ["YDB_DATABASE_TASKS"])

        auth_pool = ydb.SessionPool(auth_driver)
        firms_pool = ydb.SessionPool(firms_driver)
        tasks_pool = ydb.SessionPool(tasks_driver)
        
        # 1. Получаем инфо о пользователе
        user_info = auth_pool.retry_operation_sync(lambda s: get_user_info(s, user_id))
        
        # 2. Получаем список фирм
        user_firms = firms_pool.retry_operation_sync(lambda s: get_user_firms(s, user_id))
        
        # 3. Получаем задачи для каждой фирмы
        all_user_tasks = []
        for firm in user_firms:
            firm_id = firm['firm_id']
            try:
                tasks_for_firm = tasks_pool.retry_operation_sync(
                    lambda s: get_user_tasks_for_firm(s, firm_id, user_id)
                )
                all_user_tasks.extend(tasks_for_firm)
            except Exception as e:
                # Если таблица задач для какой-то фирмы не найдена, логируем и пропускаем
                logging.warning(f"Could not fetch tasks for firm {firm_id}. Reason: {e}")
                continue

        # 4. Собираем финальный ответ
        final_response = {
            "user_info": user_info,
            "firms": user_firms,
            "tasks": all_user_tasks
        }
        
        return {"statusCode": 200, "body": json.dumps(final_response, default=str)}

    except AuthError as e:
        return {"statusCode": 401, "body": json.dumps({"message": str(e)})}
    except NotFoundError as e:
        return {"statusCode": 404, "body": json.dumps({"message": str(e)})}
    except Exception as e:
        logging.error(f"Critical error in get-user-data: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}

```