import 'package:auto_route/auto_route.dart';
import 'package:balansoved_enterprise/features/profile/presentation/pages/profile_page.dart';
import 'package:flutter/material.dart';
import 'package:balansoved_enterprise/injection_container.dart';
import 'package:balansoved_enterprise/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:balansoved_enterprise/features/auth/presentation/pages/login_page.dart';
import 'package:balansoved_enterprise/features/auth/presentation/pages/unauthorized_page.dart';
import 'package:balansoved_enterprise/presentation/pages/home_page.dart';
import 'package:balansoved_enterprise/presentation/pages/empty_router_page.dart';
import 'package:balansoved_enterprise/features/employees_and_firms/presentation/pages/employees_page.dart';
import 'package:balansoved_enterprise/presentation/pages/welcome_page.dart';
import 'package:balansoved_enterprise/features/clients/presentation/pages/clients_page.dart';
import 'package:balansoved_enterprise/features/tasks/presentation/pages/tasks_page.dart';
import 'package:balansoved_enterprise/features/calendar/presentation/pages/calendar_page.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart'
    show TaskRequestParams;

part 'router.gr.dart';

@AutoRouterConfig()
class AppRouter extends RootStackRouter with WidgetsBindingObserver {
  AppRouter() {
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      sl<AuthCubit>().checkAuth();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  List<AutoRoute> get routes => [
    AutoRoute(
      page: UnauthorizedRoute.page,
      path: '/unauthorized',
      initial: true,
    ),
    AutoRoute(page: LoginRoute.page, path: '/login'),
    AutoRoute(
      page: HomeRoute.page,
      path: '/home',
      guards: [AuthGuard()],
      children: [
        AutoRoute(page: WelcomeRoute.page, path: '', initial: true),
        AutoRoute(page: EmployeesRoute.page, path: 'employees'),
        AutoRoute(page: ClientsRoute.page, path: 'clients'),
        AutoRoute(page: TasksRoute.page, path: 'tasks'),
        AutoRoute(page: CalendarRoute.page, path: 'calendar/:year/:month'),
        AutoRoute(page: CalendarRedirectRoute.page, path: 'calendar'),
      ],
    ),
    RedirectRoute(path: '*', redirectTo: '/unauthorized'),
  ];
}

class AuthGuard extends AutoRouteGuard {
  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) {
    final authCubit = sl<AuthCubit>();
    final currentState = authCubit.state;

    if (currentState is AuthAuthenticated) {
      resolver.next(true);
    } else if (currentState is AuthUnauthenticated ||
        currentState is AuthError) {
      authCubit.checkAuth();
      resolver.redirectUntil(const UnauthorizedRoute());
    } else {
      authCubit.checkAuth();
      resolver.redirectUntil(const UnauthorizedRoute());
    }
  }
}

/// Страница для автоматического перенаправления на календарь с текущим месяцем
@RoutePage()
class CalendarRedirectPage extends StatelessWidget {
  const CalendarRedirectPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Автоматически перенаправляем на текущий месяц
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final now = DateTime.now();

      // Используем replace, чтобы заменить страницу редиректа на сам календарь
      context.router.replace(
        CalendarRoute(initialYear: now.year, initialMonth: now.month),
      );
    });

    return const Scaffold(body: Center(child: CircularProgressIndicator()));
  }
}
