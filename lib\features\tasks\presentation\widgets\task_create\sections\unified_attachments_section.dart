import 'package:flutter/material.dart';
import '../models.dart';
import '../../../../../tariffs_and_storage/presentation/widgets/file_attachments/file_attachments.dart';

/// Секция для загрузки облачных файлов с drag & drop
class UnifiedAttachmentsSection extends StatelessWidget {
  final List<FileAttachmentItem> cloudFiles;
  final Function(FileAttachmentItem) onAddCloudFile;
  final Function(FileAttachmentItem) onRemoveCloudFile;
  final Function(FileAttachmentItem) onUpdateCloudFile;
  final VoidCallback? onTaskAutoSave;

  const UnifiedAttachmentsSection({
    super.key,
    required this.cloudFiles,
    required this.onAddCloudFile,
    required this.onRemoveCloudFile,
    required this.onUpdateCloudFile,
    this.onTaskAutoSave,
  });

  @override
  Widget build(BuildContext context) {
    return FileUploadSection(
      files: cloudFiles,
      onAddFile: onAddCloudFile,
      onRemoveFile: onRemoveCloudFile,
      onUpdateFile: onUpdateCloudFile,
      onAutoSave: onTaskAutoSave,
      title: 'Файловые вложения',
      allowedExtensions: [
        'pdf',
        'doc',
        'docx',
        'xls',
        'xlsx',
        'ppt',
        'pptx',
        'txt',
        'jpg',
        'jpeg',
        'png',
        'gif',
        'zip',
        'rar',
        '7z',
      ],
      maxFileSize: 50 * 1024 * 1024, // 50 MB
    );
  }
}
