import 'package:equatable/equatable.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';
import 'package:balansoved_enterprise/features/tasks/domain/repositories/tasks_repository.dart';

abstract class TasksState extends Equatable {
  const TasksState();

  @override
  List<Object?> get props => [];
}

class TasksInitial extends TasksState {}

class TasksLoading extends TasksState {}

class TasksLoaded extends TasksState {
  final TasksResult result;
  final TaskRequestParams currentParams;
  final bool canLoadMore;

  const TasksLoaded({
    required this.result,
    required this.currentParams,
    this.canLoadMore = false,
  });

  List<TaskEntity> get tasks => result.tasks;
  TaskPaginationMeta? get paginationMeta => result.paginationMeta;

  @override
  List<Object?> get props => [result, currentParams, canLoadMore];
}

class TasksLoadingMore extends TasksState {
  final TasksResult currentResult;
  final TaskRequestParams currentParams;

  const TasksLoadingMore({
    required this.currentResult,
    required this.currentParams,
  });

  List<TaskEntity> get tasks => currentResult.tasks;
  TaskPaginationMeta? get paginationMeta => currentResult.paginationMeta;

  @override
  List<Object?> get props => [currentResult, currentParams];
}

class TasksError extends TasksState {
  final String message;

  const TasksError({required this.message});

  @override
  List<Object?> get props => [message];
}
