
---
Идентификатор - etnh62l2bp8ieqdplhqi
Путь к базе - /ru-central1/b1g2bgg0i9r8beucbthc/etnh62l2bp8ieqdplhqi
Эндпоинт - grpcs://ydb.serverless.yandexcloud.net:2135/?database=/ru-central1/b1g2bgg0i9r8beucbthc/etnh62l2bp8ieqdplhqi

---
# Таблицы
Эта база данных использует подход "таблица на компанию" для хранения задач. Такой подход изолирует задачи одной компании от другой.

#### Таблица-шаблон: `tasks_{firm_id}`
Имя таблицы формируется динамически на основе `firm_id` компании.

| #   | Имя                       | Ключ | Тип         | Описание                                                                                                                          |
| --- | ------------------------- | ---- | ----------- | --------------------------------------------------------------------------------------------------------------------------------- |
| 0   | `task_id`                 | PK   | `Utf8`      | Уникальный идентификатор задачи (UUID).                                                                                           |
| 1   | `title`                   |      | `Utf8`      | Краткое название/заголовок задачи.                                                                                                |
| 2   | `description`             |      | `Utf8`      | Полное описание задачи.                                                                                                           |
| 3   | `client_ids_json`         |      | `Json`      | JSON-массив с `uid` клиентов, к которым относится задача.                                                                         |
| 4   | `assignee_ids_json`       |      | `Json`      | JSON-массив с `uid` сотрудников-исполнителей.                                                                                     |
| 5   | `observer_ids_json`       |      | `Json`      | JSON-массив с `uid` сотрудников-наблюдателей.                                                                                     |
| 6   | `creator_ids_json`        |      | `Json`      | JSON-массив с `uid` сотрудников-постановщиков.                                                                                    |
| 7   | `status`                  |      | `Utf8`      | Текущий статус задачи (например, "new", "in_progress", "done").                                                                   |
| 8   | `priority`                |      | `Utf8`      | Приоритет задачи ("low", "medium", "high").                                                                                       |
| 9   | `due_date`                |      | `Timestamp` | Срок выполнения задачи (дата и время).                                                                                            |
| 10  | `completed_at`            |      | `Timestamp` | Фактическое время завершения задачи.                                                                                              |
| 11  | `attachments_json`        |      | `Json`      | JSON-массив ссылок на прикрепленные файлы. Пример: `[{"name": "Счет.pdf", "url": "..."}]`                                          |
| 12  | `checklist_json`          |      | `Json`      | Чек-лист в формате JSON. Пример: `[{"text": "Подготовить отчет", "is_done": false}]`                                               |
| 13  | `reminders_json`          |      | `Json`      | JSON-массив напоминаний. Пример: `[{"datetime": "...", "role": "observer"}]`                                                       |
| 14  | `recurrence_json`         |      | `Json`      | JSON-объект с правилами повторения задачи (тип, даты, интервалы).                                                                  |
| 15  | `options_json`            |      | `Json`      | Дополнительные параметры задачи в формате JSON. Пример: `{"allow_assignee_to_change_due_date": true}`                               |
| 16  | `holiday_transfer_rule`   |      | `Utf8`      | Правило переноса на случай праздника/выходного ("next_workday", "prev_workday", "none").                                           |
| 17  | `origin_task_id`          |      | `Utf8`      | ID исходной задачи для экземпляров повторяющихся задач.                                                                           |
| 18  | `created_at`              |      | `Timestamp` | Время создания задачи.                                                                                                            |
| 19  | `updated_at`              |      | `Timestamp` | Время последнего обновления задачи.                                                                                               |