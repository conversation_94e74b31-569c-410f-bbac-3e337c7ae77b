import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:balansoved_enterprise/core/constants/constants.dart';
import 'package:balansoved_enterprise/core/error/exception.dart';
import 'package:balansoved_enterprise/core/network/network_logger.dart';
import 'package:balansoved_enterprise/features/tasks/data/data_source/tasks_remote_data_source.dart';
import 'package:balansoved_enterprise/features/tasks/data/models/task_model.dart';
import 'package:balansoved_enterprise/features/tasks/domain/entities/task_entity.dart';

class TasksRemoteDataSourceImpl implements TasksRemoteDataSource {
  final http.Client client;
  final String? Function() getToken;

  TasksRemoteDataSourceImpl({required this.client, required this.getToken});

  Map<String, String> get _headers {
    final token = getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  @override
  Future<TasksApiResult> getTasks(
    String firmId,
    TaskRequestParams params,
  ) async {
    final uri = Uri.parse(TasksApiUrls.manage());

    final requestBodyMap = <String, dynamic>{
      'firm_id': firmId,
      'action': 'GET',
    };

    // Добавляем параметры в зависимости от типа запроса
    switch (params.viewType) {
      case TaskViewType.timeless:
        // Бессрочные задачи с пагинацией
        if (params.page != null) {
          requestBodyMap['page'] = params.page;
        }
        break;

      case TaskViewType.dated:
        // Задачи с крайним сроком за месяц
        requestBodyMap['get_dated_tasks'] = true;
        if (params.month != null) {
          requestBodyMap['month'] = params.month;
        }
        if (params.year != null) {
          requestBodyMap['year'] = params.year;
        }
        break;
    }

    // Добавляем фильтр по клиенту, если он есть
    if (params.clientId != null) {
      requestBodyMap['client_id'] = params.clientId;
    }

    final requestBody = jsonEncode(requestBodyMap);

    NetworkLogger.logHttpRequest(
      method: 'POST',
      url: uri.toString(),
      headers: _headers,
      body: requestBody,
    );

    final response = await client.post(
      uri,
      headers: _headers,
      body: requestBody,
    );

    NetworkLogger.logHttpResponse(
      statusCode: response.statusCode,
      headers: response.headers,
      body: response.body,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);

      final List<TaskModel> tasks;
      TaskPaginationMeta? paginationMeta;

      if (params.viewType == TaskViewType.timeless &&
          data['metadata'] != null) {
        // Бессрочные задачи с метаданными пагинации
        tasks =
            (data['data'] as List)
                .map((task) => TaskModel.fromJson(task))
                .toList();
        paginationMeta = TaskPaginationMeta.fromJson(data['metadata']);
      } else {
        // Срочные задачи или простой список
        if (data['data'] is List) {
          tasks =
              (data['data'] as List)
                  .map((task) => TaskModel.fromJson(task))
                  .toList();
        } else {
          // Single task returned
          tasks = [TaskModel.fromJson(data['data'])];
        }
      }

      return TasksApiResult(tasks: tasks, paginationMeta: paginationMeta);
    } else {
      throw ServerException(
        message: 'Failed to fetch tasks',
        statusCode: response.statusCode,
        requestUrl: uri.toString(),
        requestBody: requestBody,
        responseBody: response.body,
      );
    }
  }

  @override
  Future<TaskModel> getTask(String firmId, String taskId) async {
    final uri = Uri.parse(TasksApiUrls.manage());
    final requestBody = jsonEncode({
      'firm_id': firmId,
      'action': 'GET',
      'task_id': taskId,
    });

    NetworkLogger.logHttpRequest(
      method: 'POST',
      url: uri.toString(),
      headers: _headers,
      body: requestBody,
    );

    final response = await client.post(
      uri,
      headers: _headers,
      body: requestBody,
    );

    NetworkLogger.logHttpResponse(
      statusCode: response.statusCode,
      headers: response.headers,
      body: response.body,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return TaskModel.fromJson(data['data']);
    } else {
      throw ServerException(
        message: 'Failed to fetch task',
        statusCode: response.statusCode,
        requestUrl: uri.toString(),
        requestBody: requestBody,
        responseBody: response.body,
      );
    }
  }

  @override
  Future<String> saveTask(String firmId, TaskModel task) async {
    final Map<String, dynamic> body = {
      'firm_id': firmId,
      'action': 'UPSERT',
      'payload': task.toJson(),
    };

    // Если у задачи есть ID, то это обновление
    if (task.id.isNotEmpty && task.id != 'new') {
      body['task_id'] = task.id;
    }

    final uri = Uri.parse(TasksApiUrls.manage());
    final requestBody = jsonEncode(body);

    NetworkLogger.logHttpRequest(
      method: 'POST',
      url: uri.toString(),
      headers: _headers,
      body: requestBody,
    );

    final response = await client.post(
      uri,
      headers: _headers,
      body: requestBody,
    );

    NetworkLogger.logHttpResponse(
      statusCode: response.statusCode,
      headers: response.headers,
      body: response.body,
    );

    if (response.statusCode == 200 || response.statusCode == 201) {
      final data = jsonDecode(response.body);
      return data['task_id'] ?? task.id;
    } else {
      throw ServerException(
        message: 'Failed to save task',
        statusCode: response.statusCode,
        requestUrl: uri.toString(),
        requestBody: requestBody,
        responseBody: response.body,
      );
    }
  }

  @override
  Future<void> deleteTask(String firmId, String taskId) async {
    final uri = Uri.parse(TasksApiUrls.manage());
    final requestBody = jsonEncode({
      'firm_id': firmId,
      'action': 'DELETE',
      'task_id': taskId,
    });

    NetworkLogger.logHttpRequest(
      method: 'POST',
      url: uri.toString(),
      headers: _headers,
      body: requestBody,
    );

    final response = await client.post(
      uri,
      headers: _headers,
      body: requestBody,
    );

    NetworkLogger.logHttpResponse(
      statusCode: response.statusCode,
      headers: response.headers,
      body: response.body,
    );

    if (response.statusCode != 200) {
      throw ServerException(
        message: 'Failed to delete task',
        statusCode: response.statusCode,
        requestUrl: uri.toString(),
        requestBody: requestBody,
        responseBody: response.body,
      );
    }
  }
}
