Идентификатор - d4er76v5l270502p7qu2
Описание - Запросить код для регистрации
Точка входа - index.handler
Таймаут - 10 сек

---

На входе:
	-> `email`: Email нового пользователя.
	-> `password`: Пароль нового пользователя.
	-> `user_name`: Имя нового пользователя.
Внутренняя работа:
    -> **Использует утилиту `utils.request_parser` для безопасного извлечения и парсинга тела запроса.**
	-> Проверяет, установлена ли переменная окружения `AUTO_CONFIRM_MODE` в `true`.
	
	**Если `AUTO_CONFIRM_MODE` = `true` (Тестовый режим):**
	1. Валидирует данные и проверяет, что email не занят.
	2. Хеширует пароль.
	3. **Создает сразу активного пользователя** в YDB (`is_active = true`).
	4. **Генерирует и возвращает JWT-токен**, минуя отправку email.
	
	**Если `AUTO_CONFIRM_MODE` = `false` (Стандартный режим):**
	5. Валидирует данные и проверяет, что email не занят.
	6. Генерирует 6-значный код и время его жизни (10 минут).
	7. Хеширует пароль.
	8. Сохраняет в YDB новую **неактивную** запись с кодом подтверждения.
	9. Отправляет код на email.

На выходе:
	-> `201 Created`: {"token": "<jwt_token>"} (Только в тестовом режиме)
	-> `200 OK`: {"message": "Verification code sent."} (В стандартном режиме)
	-> `409 Conflict`: {"message": "User with this email already exists."}
	-> `400 Bad Request`: При невалидных данных или ошибках в теле запроса.


---
#### Зависимости и окружение
- **Необходимые утилиты**: `utils/auth_utils.py`, `utils/email_utils.py`, `utils/ydb_utils.py`, `utils/request_parser.py
- **Переменные окружения**:
    - YDB_ENDPOINT - Эндпоинт базы данных
    - YDB_DATABASE - Путь к базе данных
    - SA_KEY_FILE - ydb_sa_key.json
    - JWT_SECRET - Надежная секретная строка
    - EMAIL_API_URL - URL API сервиса рассылок
    - EMAIL_API_KEY - Ключ API сервиса рассылок
	- `AUTO_CONFIRM_MODE` - `true` или `false`. (Опционально, по умолчанию `false`)

---

#### Финальная версия кода с использованием утилиты `request_parser`

```python
import json
import os
import uuid
import random
import datetime
import pytz
import logging
import ydb
from utils import ydb_utils, auth_utils, email_utils, request_parser

logging.getLogger().setLevel(logging.INFO)

def handler(event, context):
    logging.info("--- NEW INVOCATION ---")
    logging.info(f"RAW EVENT: {event}")
    
    auto_confirm_mode = os.environ.get('AUTO_CONFIRM_MODE', 'false').lower() == 'true'

    try:
        data = request_parser.parse_request_body(event)
    except ValueError as e:
        logging.error(f"Request body processing error: {e}")
        return {"statusCode": 400, "body": json.dumps({"message": str(e)})}

    email = data.get('email')
    password = data.get('password')
    user_name = data.get('user_name')

    if not all([email, password, user_name]):
        logging.error("Email, password, or user_name not provided in the parsed data.")
        return {"statusCode": 400, "body": json.dumps({"message": "Email, password, and user_name are required."})}

    driver = ydb_utils.get_ydb_driver()
    pool = ydb.SessionPool(driver)

    def transaction(session, is_auto_confirm):
        tx = session.transaction(ydb.SerializableReadWrite())

        check_query = f"PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}'); DECLARE $email AS Utf8; SELECT user_id FROM users WHERE email = $email;"
        prepared_check = session.prepare(check_query)
        result_sets = tx.execute(prepared_check, {'$email': email})
        
        if result_sets[0].rows:
            tx.rollback()
            return {"status": 409, "message": "User with this email already exists."}

        new_user_id = str(uuid.uuid4())
        hashed_password = auth_utils.hash_password(password)
        now = datetime.datetime.now(pytz.utc)

        if is_auto_confirm:
            logging.info(f"AUTO_CONFIRM_MODE: Creating active user {email}")
            upsert_query = f"""
                PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}');
                DECLARE $user_id AS Utf8; DECLARE $email AS Utf8; DECLARE $password_hash AS Utf8; DECLARE $user_name AS Utf8; DECLARE $created_at AS Timestamp;
                UPSERT INTO users (user_id, email, password_hash, user_name, created_at, is_active)
                VALUES ($user_id, $email, $password_hash, $user_name, $created_at, true);
            """
            prepared_upsert = session.prepare(upsert_query)
            tx.execute(prepared_upsert, {
                '$user_id': new_user_id, '$email': email, '$password_hash': hashed_password,
                '$user_name': user_name, '$created_at': now
            })
            tx.commit()
            
            token = auth_utils.generate_jwt(new_user_id, email)
            return {"status": 201, "token": token}
        else:
            code = str(random.randint(100000, 999999))
            expires = now + datetime.timedelta(minutes=10)
            
            upsert_query = f"""
                PRAGMA TablePathPrefix('{os.environ['YDB_DATABASE']}');
                DECLARE $user_id AS Utf8; DECLARE $email AS Utf8; DECLARE $password_hash AS Utf8; DECLARE $user_name AS Utf8;
                DECLARE $created_at AS Timestamp; DECLARE $code AS Utf8; DECLARE $expires AS Timestamp;
                UPSERT INTO users (user_id, email, password_hash, user_name, created_at, verification_code, code_expires_at, is_active)
                VALUES ($user_id, $email, $password_hash, $user_name, $created_at, $code, $expires, false);
            """
            prepared_upsert = session.prepare(upsert_query)
            tx.execute(prepared_upsert, {
                '$user_id': new_user_id, '$email': email, '$password_hash': hashed_password,
                '$user_name': user_name, '$created_at': now, '$code': code, '$expires': expires
            })
            tx.commit()

            if not email_utils.send_verification_code(email, code):
                logging.error(f"Failed to send verification code to {email}")
                return {"status": 500, "message": "Failed to send verification code."}

            return {"status": 200, "message": "Verification code sent."}

    try:
        result = pool.retry_operation_sync(lambda s: transaction(s, auto_confirm_mode))
        
        if result["status"] == 201:
            return {"statusCode": 201, "body": json.dumps({"token": result["token"]})}
        elif result["status"] == 200:
             return {"statusCode": 200, "body": json.dumps({"message": result["message"]})}
        else:
            return {"statusCode": result["status"], "body": json.dumps({"message": result["message"]})}
            
    except Exception as e:
        logging.error(f"Critical error during registration request for user {email}: {e}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"message": "Internal Server Error"})}
```